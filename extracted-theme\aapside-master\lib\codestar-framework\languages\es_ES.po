# Copyright (C) 2019 Codestar
# This file is distributed under the same license as the Codestar Framework package.
msgid ""
msgstr ""
"Project-Id-Version: Codestar Framework 2.0.2\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/codestar-"
"framework\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: \n"
"PO-Revision-Date: \n"
"Language-Team: https://doothemes.com\n"
"X-Generator: Poedit 2.2\n"
"Last-Translator: <PERSON><PERSON> <emez<PERSON>@doothemes.com>\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Language: es_ES\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-SearchPath-0: .\n"

#: classes/metabox.class.php:232 classes/options.class.php:670
msgid "No option provided by developer."
msgstr "Ninguna opción proporcionada por el desarrollador."

#: classes/metabox.class.php:250
msgid "Restore"
msgstr "Restaurar"

#: classes/metabox.class.php:251
msgid "update post for restore "
msgstr "actualizar la publicación para restaurar "

#: classes/metabox.class.php:251
msgid "Cancel"
msgstr "Cancelar"

#: classes/options.class.php:232 classes/options.class.php:388
#: functions/actions.php:43 functions/actions.php:103 functions/actions.php:125
msgid "Error while saving."
msgstr "Error durante el guardado."

#: classes/options.class.php:278
msgid "Success. Imported backup options."
msgstr "Excelente. Todas las opciones han sido importadas."

#: classes/options.class.php:300
msgid "Default options restored."
msgstr "Opciones por defecto restauradas."

#: classes/options.class.php:326
msgid "Default options restored for only this section."
msgstr "Opciones predeterminadas restauradas sólo para esta sección."

#: classes/options.class.php:384
msgid "Settings saved."
msgstr "Ajustes guardados."

#: classes/options.class.php:570
msgid "show all options"
msgstr "ver todas las opciones"

#: classes/options.class.php:572
msgid "Search option(s)"
msgstr "Buscar opciones"

#: classes/options.class.php:575 classes/options.class.php:694
msgid "Save"
msgstr "Guardar"

#: classes/options.class.php:575 classes/options.class.php:694
msgid "Saving..."
msgstr "Guardando…"

#: classes/options.class.php:576 classes/options.class.php:695
msgid "Reset Section"
msgstr "Restablecer sección"

#: classes/options.class.php:577 classes/options.class.php:696
#: fields/backup/backup.php:34
msgid "Reset All"
msgstr "Restablecer todo"

#: classes/setup.class.php:372
msgid "Ooops! This field type (%s) can not be used here, yet."
msgstr "Ooops! Este tipo de campo (%s) no se puede utilizar aquí, todavía."

#: classes/setup.class.php:414
msgid "This field class is not available!"
msgstr "¡Esta clase de campo no está disponible!"

#: classes/setup.class.php:418
msgid "This type is not found!"
msgstr "¡ Este tipo no se encuentra!"

#: classes/shortcoder.class.php:220
msgid "Add one more"
msgstr "Añadir una más"

#: classes/shortcoder.class.php:257
msgid "Security check"
msgstr "Verificación de seguridad"

#: fields/background/background.php:34
msgid "No background selected"
msgstr "No hay fondo seleccionado"

#: fields/backup/backup.php:26
msgid "Import"
msgstr "Importar"

#: fields/backup/backup.php:27
msgid "copy-paste your backup string here"
msgstr "copie-pegue su cadena de respaldo aquí"

#: fields/backup/backup.php:31
msgid "Export and Download Backup"
msgstr "Exportar y Descargar Copia de Seguridad"

#: fields/backup/backup.php:35
msgid "Please be sure for reset all of options."
msgstr "Por favor, asegúrese de restablecer todas las opciones."

#: fields/gallery/gallery.php:20
msgid "Add Gallery"
msgstr "Agregar Galería"

#: fields/gallery/gallery.php:21
msgid "Edit Gallery"
msgstr "Editar Galería"

#: fields/gallery/gallery.php:22
msgid "Clear"
msgstr "Limpiar"

#: fields/group/group.php:23
msgid "Add New"
msgstr "Añadir nuevo"

#: fields/group/group.php:24 fields/icon/icon.php:21
msgid "Remove Icon"
msgstr "Eliminar Icono"

#: fields/group/group.php:58 fields/media/media.php:24
#: fields/upload/upload.php:22
msgid "Remove"
msgstr "Quitar"

#: fields/group/group.php:113 fields/repeater/repeater.php:94
msgid "You can not add more than"
msgstr "No se puede agregar más de"

#: fields/group/group.php:114 fields/repeater/repeater.php:95
msgid "You can not remove less than"
msgstr "No se puede quitar menos de"

#: fields/icon/icon.php:20 functions/actions.php:147
msgid "Add Icon"
msgstr "Añadir Icono"

#: fields/media/media.php:23 fields/upload/upload.php:21
msgid "Upload"
msgstr "Cargar"

#: fields/media/media.php:46
msgid "No media selected"
msgstr "No se ha seleccionado ningún medio"

#: fields/select/select.php:72
msgid "No data provided for this option type."
msgstr "No hay datos proporcionados para este tipo de opción."

#: fields/sorter/sorter.php:21
msgid "Enabled"
msgstr "Habilitado"

#: fields/sorter/sorter.php:22
msgid "Disabled"
msgstr "Deshabilitado"

#: fields/textarea/textarea.php:37
msgid "Add Shortcode"
msgstr "Añadir Shortcode"

#: fields/typography/typography.php:82
msgid "Font Family"
msgstr "Familia de Fuente"

#: fields/typography/typography.php:83
msgid "Select a font"
msgstr "Seleccione una fuente"

#: fields/typography/typography.php:91
msgid "Backup Font Family"
msgstr "Tipografía de respaldo"

#: fields/typography/typography.php:105 fields/typography/typography.php:118
#: fields/typography/typography.php:131 fields/typography/typography.php:146
#: fields/typography/typography.php:162 fields/typography/typography.php:175
#: fields/typography/typography.php:189 fields/typography/typography.php:207
msgid "Default"
msgstr "Predeterminado"

#: fields/typography/typography.php:116
msgid "Font Style"
msgstr "Estilo de Fuente"

#: fields/typography/typography.php:130 fields/typography/typography.php:131
msgid "Load Extra Styles"
msgstr "Cargar estilos adicionales"

#: fields/typography/typography.php:144
msgid "Subset"
msgstr "Subconjunto"

#: fields/typography/typography.php:154
msgid "Text Align"
msgstr "Texto alineado"

#: fields/typography/typography.php:170
msgid "Font Variant"
msgstr "Variante de fuente"

#: fields/typography/typography.php:183
msgid "Text Transform"
msgstr "Transformación de texto"

#: fields/typography/typography.php:197
msgid "Text Decoration"
msgstr "Decoración del texto"

#: fields/typography/typography.php:219
msgid "Font Size"
msgstr "Tamaño de fuente"

#: fields/typography/typography.php:231
msgid "Line Height"
msgstr "Altura de la línea"

#: fields/typography/typography.php:243
msgid "Letter Spacing"
msgstr "Espacio entre letras"

#: fields/typography/typography.php:255
msgid "Word Spacing"
msgstr "Espaciado entre palabras"

#: fields/typography/typography.php:269
msgid "Font Color"
msgstr "Color de fuente"

#: fields/typography/typography.php:280
msgid "Custom Style"
msgstr "Estilo personalizado"

#: functions/actions.php:35
msgid "No data provided by developer"
msgstr "No hay datos proporcionados por el desarrollador"

#: functions/actions.php:151
msgid "Search a Icon..."
msgstr "Buscar un Icono…"

#: functions/validate.php:14 functions/validate.php:86
msgid "Please write a valid email address!"
msgstr "¡Por favor escriba una dirección válida de correo electrónico!"

#: functions/validate.php:32 functions/validate.php:106
msgid "Please write a numeric data!"
msgstr "¡Por favor introduce un dato numérico!"

#: functions/validate.php:50 functions/validate.php:126
msgid "Fatal Error! This field is required!"
msgstr "¡Error Fatal! Este dato es obligatorio!"

#: functions/validate.php:68 functions/validate.php:146
msgid "Please write a valid url!"
msgstr "Por favor, escriba una URL válida!"
