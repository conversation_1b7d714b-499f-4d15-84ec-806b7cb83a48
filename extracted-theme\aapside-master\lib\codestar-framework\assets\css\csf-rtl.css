/**
 *
 * ---------------------------------------------------------
 * CODESTAR FRAMEWORK RTL CSS MAP
 * ---------------------------------------------------------
 *
 * 01. Base
 *     01. 01. Header
 *     01. 02. Head<PERSON>
 *     01. 03. Navigation
 *     01. 04. Content
 *     01. 05. Section
 *     01. 06. Show All Options
 *     01. 07. Search Input
 *     01. 08. Copyright
 *     01. 09. Metabox
 * 02. Fields
 *     02. 01. Field: typography
 *     02. 02. Field: checkbox, radio
 *     02. 03. Field: switcher
 *     02. 04. Field: upload
 *     02. 05. Field: group
 *     02. 06. Field: repeater
 *     02. 07. Field: help
 *     02. 08. Field: icon
 *     02. 09. Field: gallery
 *     02. 10. Field: sorter
 *     02. 11. Field: tabbed
 *     02. 12. Field: media
 *     02. 13. Field: notice
 *     02. 14. Field: border, spacing, dimensions
 *     02. 15. Field: background
 *     02. 16. Field: spinner
 *     02. 17. Field: slider
 *     02. 18. Field: button_set
 *     02. 19. Field: link_color
 *     02. 20. Field: color_group
 *     02. 21. Field: palette
 * 03. Taxonomy
 * 04. Profile
 * 05. Modal
 * 06. Customizer
 * 07. Responsive
 * 08. Others
 *
 * ---------------------------------------------------------
 *
 */
/**
 * 01. Base
 */
.csf {
  margin-left: 20px;
  margin-right: 0;
}

/**
 * 01. 01. Header
 */
.csf-header h1 {
  float: right;
}
.csf-header fieldset {
  float: left;
}

/**
 * 01. 02. Header Buttons
 */
.csf-buttons {
  float: left;
  direction: ltr;
}

.csf-header-left {
  float: right;
}

.csf-header-right {
  float: left;
}

/**
 * 01. 03. Navigation
 */
.csf-nav {
  float: right;
}
.csf-nav ul {
  clear: right;
}
.csf-nav ul li .csf-section-active:after {
  right: auto;
  left: 0;
  border-left-color: #fff;
  border-right-color: transparent;
}
.csf-nav ul li .csf-arrow:after {
  content: "\f053";
  right: auto;
  left: 10px;
}
.csf-nav ul li.csf-tab-active .csf-arrow:after {
  -moz-transform: rotate(-90deg);
  -ms-transform: rotate(-90deg);
  -webkit-transform: rotate(-90deg);
  transform: rotate(-90deg);
}
.csf-nav ul ul li a {
  padding-right: 25px;
  padding-left: 15px;
}
.csf-nav ul ul:before {
  left: auto;
  right: 15px;
}
.csf-nav .fa {
  margin-left: 5px;
  margin-right: 0;
}

.csf-nav-background {
  left: auto;
  right: 0;
}

/**
 * 01. 04. Content
 */
.csf-content {
  margin-left: 0;
  margin-right: 225px;
}

/**
 * 01. 05. Section
 */
.csf-sections {
  float: right;
}

/**
 * 01. 06. Show all options
 */
.csf-show-all .csf-content {
  margin-right: 0;
  overflow: hidden;
}

.csf-expand-all {
  float: right;
  right: auto;
  left: 40px;
  margin-right: 0;
  margin-left: 4px;
}

/**
 * 01. 07. Search Input
 */
.csf-search {
  float: right;
}
.csf-search input {
  margin: 0 0 0 5px;
}

/**
 * 01. 08. Copyright
 */
.csf-copyright {
  float: right;
}

/**
 * 01. 09. Metabox
 */
.csf-metabox {
  margin: -6px -12px -12px -12px;
}
.csf-metabox .csf-section-title {
  padding: 20px;
}
.csf-metabox .csf-section-title .fa {
  margin-left: 5px;
  margin-right: 0;
}

.csf-section-title .fa {
  margin-left: 5px;
  margin-right: 0;
}

/**
 * 02. Fields
 */
.csf-field .csf-title {
  float: right;
}
.csf-field .csf-fieldset {
  margin-left: 0;
  margin-right: 30%;
}

.csf-pseudo-field {
  padding: 0 0 0 5px !important;
}

/**
 * 02. 01. Field: typography
 */
.csf-field-typography select {
  margin: 0;
  width: 100%;
}
.csf-field-typography .csf--blocks-inputs .csf--blocks {
  flex-direction: row-reverse;
}

/**
 * 02. 02. Field: checkbox, radio
 */
.csf-field-checkbox .csf--inline-list li,
.csf-field-radio .csf--inline-list li {
  margin-right: 0;
  margin-left: 15px;
}

/**
 * 02. 03. Field: switcher
 */
.csf-field-switcher .csf--switcher {
  float: right;
}
.csf-field-switcher .csf--label {
  float: right;
  margin-left: 0;
  margin-right: 5px;
}

/**
 * 02. 04. Field: upload
 */
.csf-field-upload .csf--remove,
.csf-field-upload .csf--buttons {
  margin-left: 0;
  margin-right: 5px;
}

/**
 * 02. 05. Field: group
 */
.csf-field-group .csf-cloneable-title {
  padding: 15px 10px 15px 65px;
}
.csf-field-group .csf-cloneable-helper {
  right: auto;
  left: 10px;
}

/**
 * 02. 06. Field: repeater
 */
.csf-field-repeater .csf-repeater-helper {
  border-left: 0;
  border-right: 1px solid #eee;
}

/**
 * 02. 07. Field: help
 */
.csf-help {
  right: auto;
  left: 5px;
}

/**
 * 02. 08. Field: icon
 */
.csf-field-icon .button {
  margin-right: 0;
  margin-left: 5px;
}
.csf-field-icon .csf-icon-preview i {
  margin-right: 0;
  margin-left: 5px;
}

/**
 * 02. 09. Field: gallery
 */
.csf-field-gallery ul li {
  margin-right: 0;
  margin-left: 5px;
}
.csf-field-gallery .button {
  margin-right: 0;
  margin-left: 5px;
}

/**
 * 02. 11. Field: tabbed
 */
.csf-field-tabbed .csf-tabbed-nav .fa {
  padding-right: 0;
  padding-left: 5px;
}
.csf-field-tabbed .csf-tabbed-nav a {
  margin-right: 0;
  margin-left: 5px;
}

/**
 * 02. 12. Field: media
 */
.csf-field-media .button {
  margin-left: 0;
  margin-right: 7px;
}
.csf-field-media .hidden + .button {
  margin-right: 0;
}

/**
 * 02. 13. Field: notice
 */
.csf-notice {
  border-left: none;
  border-right-style: solid;
  border-right-width: 4px;
}

/**
 * 02. 14. Field: border, spacing, dimensions
 */
.csf-field-dimensions .csf--input,
.csf-field-spacing .csf--input,
.csf-field-border .csf--input {
  float: right;
  margin-right: 0;
  margin-left: 15px;
}
.csf-field-dimensions .csf--left,
.csf-field-spacing .csf--left,
.csf-field-border .csf--left {
  float: right;
}

/**
 * 02. 15. Field: background
 */
.csf-field-background .csf--block {
  float: right;
}
.csf-field-background .csf--select,
.csf-field-background .csf--media {
  padding-right: 0;
}
.csf-field-background .csf--title {
  margin-right: 0;
  margin-left: 5px;
}

/**
 * 02. 16. Field: spinner
 */
.csf-field-spinner .csf--spin {
  float: right;
}

/**
 * 02. 17. Field: slider
 */
.csf-field-slider .ui-slider {
  direction: ltr;
}
.csf-field-slider input[type="text"] {
  margin-left: 0;
  margin-right: 15px;
}
.csf-field-slider em {
  padding-left: 0;
  padding-right: 5px;
}

/**
 * 02. 18. Field: button_set
 */
.csf-field-button_set .csf--button-group {
  float: right;
}

/**
 * 02. 19. Field: link_color
 */
.csf-field-link_color .csf--left {
  float: right;
  margin-right: 0;
  margin-left: 10px;
}

/**
 * 02. 20. Field: color_group
 */
.csf-field-color_group .csf--left {
  float: right;
  margin-right: 0;
  margin-left: 10px;
}

/**
 * 02. 21. Field: palette
 */
.csf-field-palette .csf--palette {
  margin-right: 0;
  margin-left: 10px;
}

/**
 * 03. Taxonomy
 */
.csf-taxonomy-add-fields .csf-fieldset {
  margin-right: 0;
}
.csf-taxonomy-add-fields .csf-field > .csf-fieldset > .csf-help {
  left: -5px;
  right: auto;
}

.csf-taxonomy-edit-fields .csf-fieldset {
  margin-left: 0;
  margin-right: 225px;
}
.csf-taxonomy-edit-fields .csf-field > .csf-fieldset > .csf-help {
  right: auto;
  left: -5px;
}

/**
 * 04. Profile
 */
.csf-profile > h2 > .fa {
  padding-right: 0;
  padding-left: 7px;
}
.csf-profile > .csf-field > .csf-fieldset {
  margin-left: 0;
  margin-right: 220px;
}
.csf-profile > .csf-field > .csf-help {
  left: 0;
  right: auto;
}

.csf-taxonomy-edit-fields .csf-fieldset {
  margin-left: 0;
  margin-right: 225px;
}
.csf-taxonomy-edit-fields .csf-field > .csf-fieldset > .csf-help {
  right: auto;
  left: -5px;
}

/**
 * 05. Modal
 */
.csf-modal-content .csf-field {
  padding: 15px 15px 15px 30px;
}

.csf-modal-title {
  padding: 0 16px 0 36px;
}

.csf-modal-close {
  right: auto;
  left: 0;
}

/**
 * 06. Customizer
 */
.control-section .csf-field .csf-fieldset {
  margin-right: 0;
}

/**
 * 07. Responsive
 */
@media only screen and (max-width: 1200px) {
  .csf-metabox .csf-field .csf-fieldset {
    margin-left: 0;
  }
}
@media only screen and (max-width: 782px) {
  .csf .csf-fieldset,
  .csf .csf-content {
    margin-right: 0;
  }
}
/**
 * 08. Others
 */
.csf-field .csf--transparent-slider {
  margin-left: 0;
  margin-right: 2px;
}
.csf-field .csf--transparent-slider .ui-slider-handle {
  margin: 0 -11px;
}
.csf-field .csf--transparent-offset {
  background-position: center right;
}
.csf-field .csf--transparent-text {
  right: auto;
  left: 10px;
}
