<?php if ( ! defined( 'ABSPATH' ) ) { die; } // Cannot access directly. ?>

<h3>v2.0.7</h3>
<ul>
  <li>Added: Profile Options Framework (bonus).</li>
  <li>Added: Media field fallback function for easy migration from v1.x to v2.x.</li>
  <li>Fixed: A minor PHP notice in the Shortcode Generate Framework.</li>
  <li>Improved: Link Color field output for given array elements.</li>
  <li>Improved: Elementor integration for Shortcode Generate Framework.</li>
  <li>Improved: Backup field UI fallback in the Customize Framework.</li>
  <li>Improved: Global dependency controls.</li>
</ul>

<h3>v2.0.6</h3>
<ul>
  <li>Added: Default values from a external array as optional for all frameworks.</li>
  <li>Added: Widget Title sync support in Widgets Framework.</li>
  <li>Added: WP Roles select field options.</li>
  <li>Fixed: Initialization issue inside after_theme_setup action.</li>
  <li>Fixed: Backup export button issue.</li>
  <li>Fixed: WP Media uploaded item selected issue.</li>
  <li>Improved: Reset and Import UI messages in the Customize Framework.</li>
  <li>Improved: Translation .POT file.</li>
  <li>Improved: Global dependency controls.</li>
</ul>

<h3>v2.0.5</h3>
<ul>
  <li>Added: Dependency controls for among separate sections fields.</li>
  <li>Added: Border field new border style properties.</li>
  <li>Added: Taxonomy Framework section title param.</li>
  <li>Fixed: Metabox section title issue.</li>
  <li>Fixed: Typography refresh issue in Customizer.</li>
  <li>Fixed: Group and Repeater fields without title parameter.</li>
  <li>Fixed: Color field default issue in Background, Typography, Border fields.</li>
  <li>Fixed: RevSlider CodeMirror conflict.</li>
  <li>Fixed: Shortcode Generate Framework Group/Repeater nested issue.</li>
</ul>

<h3>v2.0.4</h3>
<ul>
  <li>Added: Dark and Light themes.</li>
  <li>Added: New params to change on/off texts for Switcher field.</li>
  <li>Added: Shortcode generate framework support for Elementor text editor.</li>
  <li>Fixed: Sortable and Sorter fields ordering save issue in Customizer.</li>
  <li>Fixed: Radio, Button Set and Image Select fields issue in Group field.</li>
  <li>Fixed: Color picker default/clear button issue in Customizer.</li>
  <li>Improved: RTL style of framework.</li>
  <li>Improved: Media and Upload fields remove buttons.</li>
  <li>Improved: Framework style css.</li>
  <li>Changed: Backup field data type "json" instead of "serialize".</li>
</ul>

<h3>v2.0.3</h3>
<ul>
  <li>Added: Widget Options Framework (bonus).</li>
  <li>Added: Nested Group support.</li>
  <li>Added: Nested Repeater support.</li>
  <li>Added: Spanish Translation po/mo.</li>
  <li>Added: Date range "from" and "to" for Date field.</li>
  <li>Added: New param "empty_message" if options not provided for Select, Checkbox, Radio.</li>
  <li>Fixed: Metabox framework php notices in 404 page etc.</li>
  <li>Fixed: WP Editor field save issue.</li>
  <li>Improved: Validate email function.</li>
  <li>Improved: Group field arguments.</li>
  <li>Improved: Font-Awesome library.</li>
  <li>Improved: Hide to "welcome" page automatically if not used as plugin.</li>
  <li>Improved: Confirm alert box messages translations.</li>
</ul>

<h3>v2.0.2</h3>
<ul>
  <li>Added: Page Templates "default" option for spesific metabox hide/show.</li>
  <li>Added: Post Formats "default" option for spesific metabox hide/show.</li>
  <li>Added: Only allow number inputs for Spacing, Dimensions, Border, Slider, Spinner, Typography etc.</li>
  <li>Added: ChosenJS custom options support and improved width and css style.</li>
  <li>Fixed: Taxonomy framework jquery triggers. It was not working again after saving. It fixed now.</li>
  <li>Fixed: Code Editor style issue for used inside Group/Repeater.</li>
  <li>Fixed: Sortable field sortby issue.</li>
  <li>Fixed: Options panel show in customizer option.</li>
  <li>Fixed: Media field URL show/hide option issue.</li>
  <li>Improved: Typography, Color, Spinner, Date, Chosen, Slider fields for used inside Group/Repeater.</li>
  <li>Improved: All fields javascript triggers for more performance.</li>
  <li>Improved: Customizer Framework field dependency feature.</li>
  <li>Improved: Customizer Framework field styles.</li>
</ul>

<h3>v2.0.1</h3>
<ul>
  <li>Added: WP Editor AJAX support for Group Field, Repeater Field and Everywhere.</li>
  <li>Added: Custom palette colors option for Color Picker.</li>
  <li>Added: Override files feature again.</li>
  <li>Added: Validate URL function callback.</li>
  <li>Fixed: Group field save issue.</li>
  <li>Fixed: Multiple given post formats metabox hide/show issue.</li>
  <li>Fixed: Minor bugs.</li>
</ul>

<h3>v2.0.0</h3>
<ul>
  <li>Premium version release.</li>
</ul>

<h3>v1.0.0</h3>
<ul>
  <li>Initial release.</li>
</ul>

