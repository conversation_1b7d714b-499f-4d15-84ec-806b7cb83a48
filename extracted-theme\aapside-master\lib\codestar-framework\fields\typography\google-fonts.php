<?php if ( ! defined( 'ABSPATH' ) ) { die; } // Cannot access directly.

if( ! function_exists( 'csf_get_google_fonts' ) ) {
  function csf_get_google_fonts() {
    return array(
      '<PERSON>eeZee' => array( array('normal','italic'), array('latin') ),
      'Abel' => array( array('normal'), array('latin') ),
      'Abhaya Libre' => array( array('normal','500','600','700','800'), array('latin-ext','latin','sinhala') ),
      'Abril Fatface' => array( array('normal'), array('latin-ext','latin') ),
      'Aclonica' => array( array('normal'), array('latin') ),
      'Acme' => array( array('normal'), array('latin') ),
      'Actor' => array( array('normal'), array('latin') ),
      'Adamina' => array( array('normal'), array('latin') ),
      'Advent Pro' => array( array('100','200','300','normal','500','600','700'), array('latin-ext','greek','latin') ),
      '<PERSON>gua<PERSON><PERSON> Script' => array( array('normal'), array('latin-ext','latin') ),
      'Akronim' => array( array('normal'), array('latin-ext','latin') ),
      'Aladin' => array( array('normal'), array('latin-ext','latin') ),
      'Aldrich' => array( array('normal'), array('latin') ),
      'Alef' => array( array('normal','700'), array('latin','hebrew') ),
      'Alegreya' => array( array('normal','italic','500','500italic','700','700italic','800','800italic','900','900italic'), array('latin-ext','greek-ext','greek','latin','cyrillic','vietnamese','cyrillic-ext') ),
      'Alegreya SC' => array( array('normal','italic','500','500italic','700','700italic','800','800italic','900','900italic'), array('latin-ext','greek-ext','greek','latin','cyrillic','vietnamese','cyrillic-ext') ),
      'Alegreya Sans' => array( array('100','100italic','300','300italic','normal','italic','500','500italic','700','700italic','800','800italic','900','900italic'), array('latin-ext','greek-ext','greek','latin','cyrillic','vietnamese','cyrillic-ext') ),
      'Alegreya Sans SC' => array( array('100','100italic','300','300italic','normal','italic','500','500italic','700','700italic','800','800italic','900','900italic'), array('latin-ext','greek-ext','greek','latin','cyrillic','vietnamese','cyrillic-ext') ),
      'Alex Brush' => array( array('normal'), array('latin-ext','latin') ),
      'Alfa Slab One' => array( array('normal'), array('latin-ext','latin','vietnamese') ),
      'Alice' => array( array('normal'), array('latin','cyrillic','cyrillic-ext') ),
      'Alike' => array( array('normal'), array('latin') ),
      'Alike Angular' => array( array('normal'), array('latin') ),
      'Allan' => array( array('normal','700'), array('latin-ext','latin') ),
      'Allerta' => array( array('normal'), array('latin') ),
      'Allerta Stencil' => array( array('normal'), array('latin') ),
      'Allura' => array( array('normal'), array('latin-ext','latin') ),
      'Almendra' => array( array('normal','italic','700','700italic'), array('latin-ext','latin') ),
      'Almendra Display' => array( array('normal'), array('latin-ext','latin') ),
      'Almendra SC' => array( array('normal'), array('latin') ),
      'Amarante' => array( array('normal'), array('latin-ext','latin') ),
      'Amaranth' => array( array('normal','italic','700','700italic'), array('latin') ),
      'Amatic SC' => array( array('normal','700'), array('latin-ext','latin','cyrillic','vietnamese','hebrew') ),
      'Amethysta' => array( array('normal'), array('latin') ),
      'Amiko' => array( array('normal','600','700'), array('latin-ext','devanagari','latin') ),
      'Amiri' => array( array('normal','italic','700','700italic'), array('latin-ext','arabic','latin') ),
      'Amita' => array( array('normal','700'), array('latin-ext','devanagari','latin') ),
      'Anaheim' => array( array('normal'), array('latin-ext','latin') ),
      'Andada' => array( array('normal'), array('latin-ext','latin') ),
      'Andika' => array( array('normal'), array('latin-ext','latin','cyrillic','vietnamese','cyrillic-ext') ),
      'Angkor' => array( array('normal'), array('khmer') ),
      'Annie Use Your Telescope' => array( array('normal'), array('latin') ),
      'Anonymous Pro' => array( array('normal','italic','700','700italic'), array('latin-ext','greek','latin','cyrillic') ),
      'Antic' => array( array('normal'), array('latin') ),
      'Antic Didone' => array( array('normal'), array('latin') ),
      'Antic Slab' => array( array('normal'), array('latin') ),
      'Anton' => array( array('normal'), array('latin-ext','latin','vietnamese') ),
      'Arapey' => array( array('normal','italic'), array('latin') ),
      'Arbutus' => array( array('normal'), array('latin-ext','latin') ),
      'Arbutus Slab' => array( array('normal'), array('latin-ext','latin') ),
      'Architects Daughter' => array( array('normal'), array('latin') ),
      'Archivo' => array( array('normal','italic','500','500italic','600','600italic','700','700italic'), array('latin-ext','latin','vietnamese') ),
      'Archivo Black' => array( array('normal'), array('latin-ext','latin') ),
      'Archivo Narrow' => array( array('normal','italic','500','500italic','600','600italic','700','700italic'), array('latin-ext','latin') ),
      'Aref Ruqaa' => array( array('normal','700'), array('arabic','latin') ),
      'Arima Madurai' => array( array('100','200','300','normal','500','700','800','900'), array('latin-ext','tamil','latin','vietnamese') ),
      'Arimo' => array( array('normal','italic','700','700italic'), array('latin-ext','greek-ext','greek','latin','cyrillic','vietnamese','hebrew','cyrillic-ext') ),
      'Arizonia' => array( array('normal'), array('latin-ext','latin') ),
      'Armata' => array( array('normal'), array('latin-ext','latin') ),
      'Arsenal' => array( array('normal','italic','700','700italic'), array('latin-ext','latin','cyrillic','vietnamese','cyrillic-ext') ),
      'Artifika' => array( array('normal'), array('latin') ),
      'Arvo' => array( array('normal','italic','700','700italic'), array('latin') ),
      'Arya' => array( array('normal','700'), array('latin-ext','devanagari','latin') ),
      'Asap' => array( array('normal','italic','500','500italic','600','600italic','700','700italic'), array('latin-ext','latin','vietnamese') ),
      'Asap Condensed' => array( array('normal','italic','500','500italic','600','600italic','700','700italic'), array('latin-ext','latin','vietnamese') ),
      'Asar' => array( array('normal'), array('latin-ext','devanagari','latin') ),
      'Asset' => array( array('normal'), array('latin') ),
      'Assistant' => array( array('200','300','normal','600','700','800'), array('latin','hebrew') ),
      'Astloch' => array( array('normal','700'), array('latin') ),
      'Asul' => array( array('normal','700'), array('latin') ),
      'Athiti' => array( array('200','300','normal','500','600','700'), array('latin-ext','thai','latin','vietnamese') ),
      'Atma' => array( array('300','normal','500','600','700'), array('latin-ext','bengali','latin') ),
      'Atomic Age' => array( array('normal'), array('latin') ),
      'Aubrey' => array( array('normal'), array('latin') ),
      'Audiowide' => array( array('normal'), array('latin-ext','latin') ),
      'Autour One' => array( array('normal'), array('latin-ext','latin') ),
      'Average' => array( array('normal'), array('latin-ext','latin') ),
      'Average Sans' => array( array('normal'), array('latin-ext','latin') ),
      'Averia Gruesa Libre' => array( array('normal'), array('latin-ext','latin') ),
      'Averia Libre' => array( array('300','300italic','normal','italic','700','700italic'), array('latin') ),
      'Averia Sans Libre' => array( array('300','300italic','normal','italic','700','700italic'), array('latin') ),
      'Averia Serif Libre' => array( array('300','300italic','normal','italic','700','700italic'), array('latin') ),
      'Bad Script' => array( array('normal'), array('latin','cyrillic') ),
      'Bahiana' => array( array('normal'), array('latin-ext','latin') ),
      'Bai Jamjuree' => array( array('200','200italic','300','300italic','normal','italic','500','500italic','600','600italic','700','700italic'), array('latin-ext','thai','latin','vietnamese') ),
      'Baloo' => array( array('normal'), array('latin-ext','devanagari','latin','vietnamese') ),
      'Baloo Bhai' => array( array('normal'), array('latin-ext','latin','vietnamese','gujarati') ),
      'Baloo Bhaijaan' => array( array('normal'), array('latin-ext','arabic','latin','vietnamese') ),
      'Baloo Bhaina' => array( array('normal'), array('latin-ext','latin','vietnamese','oriya') ),
      'Baloo Chettan' => array( array('normal'), array('latin-ext','latin','vietnamese','malayalam') ),
      'Baloo Da' => array( array('normal'), array('latin-ext','bengali','latin','vietnamese') ),
      'Baloo Paaji' => array( array('normal'), array('latin-ext','latin','vietnamese','gurmukhi') ),
      'Baloo Tamma' => array( array('normal'), array('latin-ext','latin','vietnamese','kannada') ),
      'Baloo Tammudu' => array( array('normal'), array('latin-ext','latin','vietnamese','telugu') ),
      'Baloo Thambi' => array( array('normal'), array('latin-ext','tamil','latin','vietnamese') ),
      'Balthazar' => array( array('normal'), array('latin') ),
      'Bangers' => array( array('normal'), array('latin-ext','latin','vietnamese') ),
      'Barlow' => array( array('100','100italic','200','200italic','300','300italic','normal','italic','500','500italic','600','600italic','700','700italic','800','800italic','900','900italic'), array('latin-ext','latin') ),
      'Barlow Condensed' => array( array('100','100italic','200','200italic','300','300italic','normal','italic','500','500italic','600','600italic','700','700italic','800','800italic','900','900italic'), array('latin-ext','latin') ),
      'Barlow Semi Condensed' => array( array('100','100italic','200','200italic','300','300italic','normal','italic','500','500italic','600','600italic','700','700italic','800','800italic','900','900italic'), array('latin-ext','latin') ),
      'Barrio' => array( array('normal'), array('latin-ext','latin') ),
      'Basic' => array( array('normal'), array('latin-ext','latin') ),
      'Battambang' => array( array('normal','700'), array('khmer') ),
      'Baumans' => array( array('normal'), array('latin') ),
      'Bayon' => array( array('normal'), array('khmer') ),
      'Belgrano' => array( array('normal'), array('latin') ),
      'Bellefair' => array( array('normal'), array('latin-ext','latin','hebrew') ),
      'Belleza' => array( array('normal'), array('latin-ext','latin') ),
      'BenchNine' => array( array('300','normal','700'), array('latin-ext','latin') ),
      'Bentham' => array( array('normal'), array('latin') ),
      'Berkshire Swash' => array( array('normal'), array('latin-ext','latin') ),
      'Bevan' => array( array('normal'), array('latin-ext','latin','vietnamese') ),
      'Bigelow Rules' => array( array('normal'), array('latin-ext','latin') ),
      'Bigshot One' => array( array('normal'), array('latin') ),
      'Bilbo' => array( array('normal'), array('latin-ext','latin') ),
      'Bilbo Swash Caps' => array( array('normal'), array('latin-ext','latin') ),
      'BioRhyme' => array( array('200','300','normal','700','800'), array('latin-ext','latin') ),
      'BioRhyme Expanded' => array( array('200','300','normal','700','800'), array('latin-ext','latin') ),
      'Biryani' => array( array('200','300','normal','600','700','800','900'), array('latin-ext','devanagari','latin') ),
      'Bitter' => array( array('normal','italic','700'), array('latin-ext','latin') ),
      'Black And White Picture' => array( array('normal'), array('latin','korean') ),
      'Black Han Sans' => array( array('normal'), array('latin','korean') ),
      'Black Ops One' => array( array('normal'), array('latin-ext','latin') ),
      'Bokor' => array( array('normal'), array('khmer') ),
      'Bonbon' => array( array('normal'), array('latin') ),
      'Boogaloo' => array( array('normal'), array('latin') ),
      'Bowlby One' => array( array('normal'), array('latin') ),
      'Bowlby One SC' => array( array('normal'), array('latin-ext','latin') ),
      'Brawler' => array( array('normal'), array('latin') ),
      'Bree Serif' => array( array('normal'), array('latin-ext','latin') ),
      'Bubblegum Sans' => array( array('normal'), array('latin-ext','latin') ),
      'Bubbler One' => array( array('normal'), array('latin-ext','latin') ),
      'Buda' => array( array('300'), array('latin') ),
      'Buenard' => array( array('normal','700'), array('latin-ext','latin') ),
      'Bungee' => array( array('normal'), array('latin-ext','latin','vietnamese') ),
      'Bungee Hairline' => array( array('normal'), array('latin-ext','latin','vietnamese') ),
      'Bungee Inline' => array( array('normal'), array('latin-ext','latin','vietnamese') ),
      'Bungee Outline' => array( array('normal'), array('latin-ext','latin','vietnamese') ),
      'Bungee Shade' => array( array('normal'), array('latin-ext','latin','vietnamese') ),
      'Butcherman' => array( array('normal'), array('latin-ext','latin') ),
      'Butterfly Kids' => array( array('normal'), array('latin-ext','latin') ),
      'Cabin' => array( array('normal','italic','500','500italic','600','600italic','700','700italic'), array('latin-ext','latin','vietnamese') ),
      'Cabin Condensed' => array( array('normal','500','600','700'), array('latin-ext','latin','vietnamese') ),
      'Cabin Sketch' => array( array('normal','700'), array('latin') ),
      'Caesar Dressing' => array( array('normal'), array('latin') ),
      'Cagliostro' => array( array('normal'), array('latin') ),
      'Cairo' => array( array('200','300','normal','600','700','900'), array('latin-ext','arabic','latin') ),
      'Calligraffitti' => array( array('normal'), array('latin') ),
      'Cambay' => array( array('normal','italic','700','700italic'), array('latin-ext','devanagari','latin') ),
      'Cambo' => array( array('normal'), array('latin') ),
      'Candal' => array( array('normal'), array('latin') ),
      'Cantarell' => array( array('normal','italic','700','700italic'), array('latin') ),
      'Cantata One' => array( array('normal'), array('latin-ext','latin') ),
      'Cantora One' => array( array('normal'), array('latin-ext','latin') ),
      'Capriola' => array( array('normal'), array('latin-ext','latin') ),
      'Cardo' => array( array('normal','italic','700'), array('latin-ext','greek-ext','greek','latin') ),
      'Carme' => array( array('normal'), array('latin') ),
      'Carrois Gothic' => array( array('normal'), array('latin') ),
      'Carrois Gothic SC' => array( array('normal'), array('latin') ),
      'Carter One' => array( array('normal'), array('latin') ),
      'Catamaran' => array( array('100','200','300','normal','500','600','700','800','900'), array('latin-ext','tamil','latin') ),
      'Caudex' => array( array('normal','italic','700','700italic'), array('latin-ext','greek-ext','greek','latin') ),
      'Caveat' => array( array('normal','700'), array('latin-ext','latin','cyrillic') ),
      'Caveat Brush' => array( array('normal'), array('latin-ext','latin') ),
      'Cedarville Cursive' => array( array('normal'), array('latin') ),
      'Ceviche One' => array( array('normal'), array('latin-ext','latin') ),
      'Chakra Petch' => array( array('300','300italic','normal','italic','500','500italic','600','600italic','700','700italic'), array('latin-ext','thai','latin','vietnamese') ),
      'Changa' => array( array('200','300','normal','500','600','700','800'), array('latin-ext','arabic','latin') ),
      'Changa One' => array( array('normal','italic'), array('latin') ),
      'Chango' => array( array('normal'), array('latin-ext','latin') ),
      'Charmonman' => array( array('normal','700'), array('latin-ext','thai','latin','vietnamese') ),
      'Chathura' => array( array('100','300','normal','700','800'), array('latin','telugu') ),
      'Chau Philomene One' => array( array('normal','italic'), array('latin-ext','latin') ),
      'Chela One' => array( array('normal'), array('latin-ext','latin') ),
      'Chelsea Market' => array( array('normal'), array('latin-ext','latin') ),
      'Chenla' => array( array('normal'), array('khmer') ),
      'Cherry Cream Soda' => array( array('normal'), array('latin') ),
      'Cherry Swash' => array( array('normal','700'), array('latin-ext','latin') ),
      'Chewy' => array( array('normal'), array('latin') ),
      'Chicle' => array( array('normal'), array('latin-ext','latin') ),
      'Chivo' => array( array('300','300italic','normal','italic','700','700italic','900','900italic'), array('latin-ext','latin') ),
      'Chonburi' => array( array('normal'), array('latin-ext','thai','latin','vietnamese') ),
      'Cinzel' => array( array('normal','700','900'), array('latin-ext','latin') ),
      'Cinzel Decorative' => array( array('normal','700','900'), array('latin') ),
      'Clicker Script' => array( array('normal'), array('latin-ext','latin') ),
      'Coda' => array( array('normal','800'), array('latin-ext','latin') ),
      'Coda Caption' => array( array('800'), array('latin-ext','latin') ),
      'Codystar' => array( array('300','normal'), array('latin-ext','latin') ),
      'Coiny' => array( array('normal'), array('latin-ext','tamil','latin','vietnamese') ),
      'Combo' => array( array('normal'), array('latin-ext','latin') ),
      'Comfortaa' => array( array('300','normal','700'), array('latin-ext','greek','latin','cyrillic','vietnamese','cyrillic-ext') ),
      'Coming Soon' => array( array('normal'), array('latin') ),
      'Concert One' => array( array('normal'), array('latin-ext','latin') ),
      'Condiment' => array( array('normal'), array('latin-ext','latin') ),
      'Content' => array( array('normal','700'), array('khmer') ),
      'Contrail One' => array( array('normal'), array('latin') ),
      'Convergence' => array( array('normal'), array('latin') ),
      'Cookie' => array( array('normal'), array('latin') ),
      'Copse' => array( array('normal'), array('latin') ),
      'Corben' => array( array('normal','700'), array('latin-ext','latin') ),
      'Cormorant' => array( array('300','300italic','normal','italic','500','500italic','600','600italic','700','700italic'), array('latin-ext','latin','cyrillic','vietnamese','cyrillic-ext') ),
      'Cormorant Garamond' => array( array('300','300italic','normal','italic','500','500italic','600','600italic','700','700italic'), array('latin-ext','latin','cyrillic','vietnamese','cyrillic-ext') ),
      'Cormorant Infant' => array( array('300','300italic','normal','italic','500','500italic','600','600italic','700','700italic'), array('latin-ext','latin','cyrillic','vietnamese','cyrillic-ext') ),
      'Cormorant SC' => array( array('300','normal','500','600','700'), array('latin-ext','latin','cyrillic','vietnamese','cyrillic-ext') ),
      'Cormorant Unicase' => array( array('300','normal','500','600','700'), array('latin-ext','latin','cyrillic','vietnamese','cyrillic-ext') ),
      'Cormorant Upright' => array( array('300','normal','500','600','700'), array('latin-ext','latin','vietnamese') ),
      'Courgette' => array( array('normal'), array('latin-ext','latin') ),
      'Cousine' => array( array('normal','italic','700','700italic'), array('latin-ext','greek-ext','greek','latin','cyrillic','vietnamese','hebrew','cyrillic-ext') ),
      'Coustard' => array( array('normal','900'), array('latin') ),
      'Covered By Your Grace' => array( array('normal'), array('latin') ),
      'Crafty Girls' => array( array('normal'), array('latin') ),
      'Creepster' => array( array('normal'), array('latin') ),
      'Crete Round' => array( array('normal','italic'), array('latin-ext','latin') ),
      'Crimson Text' => array( array('normal','italic','600','600italic','700','700italic'), array('latin') ),
      'Croissant One' => array( array('normal'), array('latin-ext','latin') ),
      'Crushed' => array( array('normal'), array('latin') ),
      'Cuprum' => array( array('normal','italic','700','700italic'), array('latin-ext','latin','cyrillic','vietnamese','cyrillic-ext') ),
      'Cute Font' => array( array('normal'), array('latin','korean') ),
      'Cutive' => array( array('normal'), array('latin-ext','latin') ),
      'Cutive Mono' => array( array('normal'), array('latin-ext','latin') ),
      'Damion' => array( array('normal'), array('latin') ),
      'Dancing Script' => array( array('normal','700'), array('latin-ext','latin','vietnamese') ),
      'Dangrek' => array( array('normal'), array('khmer') ),
      'David Libre' => array( array('normal','500','700'), array('latin-ext','latin','vietnamese','hebrew') ),
      'Dawning of a New Day' => array( array('normal'), array('latin') ),
      'Days One' => array( array('normal'), array('latin') ),
      'Dekko' => array( array('normal'), array('latin-ext','devanagari','latin') ),
      'Delius' => array( array('normal'), array('latin') ),
      'Delius Swash Caps' => array( array('normal'), array('latin') ),
      'Delius Unicase' => array( array('normal','700'), array('latin') ),
      'Della Respira' => array( array('normal'), array('latin') ),
      'Denk One' => array( array('normal'), array('latin-ext','latin') ),
      'Devonshire' => array( array('normal'), array('latin-ext','latin') ),
      'Dhurjati' => array( array('normal'), array('latin','telugu') ),
      'Didact Gothic' => array( array('normal'), array('latin-ext','greek-ext','greek','latin','cyrillic','cyrillic-ext') ),
      'Diplomata' => array( array('normal'), array('latin-ext','latin') ),
      'Diplomata SC' => array( array('normal'), array('latin-ext','latin') ),
      'Do Hyeon' => array( array('normal'), array('latin','korean') ),
      'Dokdo' => array( array('normal'), array('latin','korean') ),
      'Domine' => array( array('normal','700'), array('latin-ext','latin') ),
      'Donegal One' => array( array('normal'), array('latin-ext','latin') ),
      'Doppio One' => array( array('normal'), array('latin-ext','latin') ),
      'Dorsa' => array( array('normal'), array('latin') ),
      'Dosis' => array( array('200','300','normal','500','600','700','800'), array('latin-ext','latin') ),
      'Dr Sugiyama' => array( array('normal'), array('latin-ext','latin') ),
      'Duru Sans' => array( array('normal'), array('latin-ext','latin') ),
      'Dynalight' => array( array('normal'), array('latin-ext','latin') ),
      'EB Garamond' => array( array('normal','italic','500','500italic','600','600italic','700','700italic','800','800italic'), array('latin-ext','greek-ext','greek','latin','cyrillic','vietnamese','cyrillic-ext') ),
      'Eagle Lake' => array( array('normal'), array('latin-ext','latin') ),
      'East Sea Dokdo' => array( array('normal'), array('latin','korean') ),
      'Eater' => array( array('normal'), array('latin-ext','latin') ),
      'Economica' => array( array('normal','italic','700','700italic'), array('latin-ext','latin') ),
      'Eczar' => array( array('normal','500','600','700','800'), array('latin-ext','devanagari','latin') ),
      'El Messiri' => array( array('normal','500','600','700'), array('arabic','latin','cyrillic') ),
      'Electrolize' => array( array('normal'), array('latin') ),
      'Elsie' => array( array('normal','900'), array('latin-ext','latin') ),
      'Elsie Swash Caps' => array( array('normal','900'), array('latin-ext','latin') ),
      'Emblema One' => array( array('normal'), array('latin-ext','latin') ),
      'Emilys Candy' => array( array('normal'), array('latin-ext','latin') ),
      'Encode Sans' => array( array('100','200','300','normal','500','600','700','800','900'), array('latin-ext','latin','vietnamese') ),
      'Encode Sans Condensed' => array( array('100','200','300','normal','500','600','700','800','900'), array('latin-ext','latin','vietnamese') ),
      'Encode Sans Expanded' => array( array('100','200','300','normal','500','600','700','800','900'), array('latin-ext','latin','vietnamese') ),
      'Encode Sans Semi Condensed' => array( array('100','200','300','normal','500','600','700','800','900'), array('latin-ext','latin','vietnamese') ),
      'Encode Sans Semi Expanded' => array( array('100','200','300','normal','500','600','700','800','900'), array('latin-ext','latin','vietnamese') ),
      'Engagement' => array( array('normal'), array('latin') ),
      'Englebert' => array( array('normal'), array('latin-ext','latin') ),
      'Enriqueta' => array( array('normal','700'), array('latin-ext','latin') ),
      'Erica One' => array( array('normal'), array('latin-ext','latin') ),
      'Esteban' => array( array('normal'), array('latin-ext','latin') ),
      'Euphoria Script' => array( array('normal'), array('latin-ext','latin') ),
      'Ewert' => array( array('normal'), array('latin-ext','latin') ),
      'Exo' => array( array('100','100italic','200','200italic','300','300italic','normal','italic','500','500italic','600','600italic','700','700italic','800','800italic','900','900italic'), array('latin-ext','latin','vietnamese') ),
      'Exo 2' => array( array('100','100italic','200','200italic','300','300italic','normal','italic','500','500italic','600','600italic','700','700italic','800','800italic','900','900italic'), array('latin-ext','latin','cyrillic') ),
      'Expletus Sans' => array( array('normal','italic','500','500italic','600','600italic','700','700italic'), array('latin') ),
      'Fahkwang' => array( array('200','200italic','300','300italic','normal','italic','500','500italic','600','600italic','700','700italic'), array('latin-ext','thai','latin','vietnamese') ),
      'Fanwood Text' => array( array('normal','italic'), array('latin') ),
      'Farsan' => array( array('normal'), array('latin-ext','latin','vietnamese','gujarati') ),
      'Fascinate' => array( array('normal'), array('latin') ),
      'Fascinate Inline' => array( array('normal'), array('latin') ),
      'Faster One' => array( array('normal'), array('latin') ),
      'Fasthand' => array( array('normal'), array('khmer') ),
      'Fauna One' => array( array('normal'), array('latin-ext','latin') ),
      'Faustina' => array( array('normal','italic','500','500italic','600','600italic','700','700italic'), array('latin-ext','latin','vietnamese') ),
      'Federant' => array( array('normal'), array('latin') ),
      'Federo' => array( array('normal'), array('latin') ),
      'Felipa' => array( array('normal'), array('latin-ext','latin') ),
      'Fenix' => array( array('normal'), array('latin-ext','latin') ),
      'Finger Paint' => array( array('normal'), array('latin') ),
      'Fira Mono' => array( array('normal','500','700'), array('latin-ext','greek-ext','greek','latin','cyrillic','cyrillic-ext') ),
      'Fira Sans' => array( array('100','100italic','200','200italic','300','300italic','normal','italic','500','500italic','600','600italic','700','700italic','800','800italic','900','900italic'), array('latin-ext','greek-ext','greek','latin','cyrillic','vietnamese','cyrillic-ext') ),
      'Fira Sans Condensed' => array( array('100','100italic','200','200italic','300','300italic','normal','italic','500','500italic','600','600italic','700','700italic','800','800italic','900','900italic'), array('latin-ext','greek-ext','greek','latin','cyrillic','vietnamese','cyrillic-ext') ),
      'Fira Sans Extra Condensed' => array( array('100','100italic','200','200italic','300','300italic','normal','italic','500','500italic','600','600italic','700','700italic','800','800italic','900','900italic'), array('latin-ext','greek-ext','greek','latin','cyrillic','vietnamese','cyrillic-ext') ),
      'Fjalla One' => array( array('normal'), array('latin-ext','latin') ),
      'Fjord One' => array( array('normal'), array('latin') ),
      'Flamenco' => array( array('300','normal'), array('latin') ),
      'Flavors' => array( array('normal'), array('latin') ),
      'Fondamento' => array( array('normal','italic'), array('latin-ext','latin') ),
      'Fontdiner Swanky' => array( array('normal'), array('latin') ),
      'Forum' => array( array('normal'), array('latin-ext','latin','cyrillic','cyrillic-ext') ),
      'Francois One' => array( array('normal'), array('latin-ext','latin','vietnamese') ),
      'Frank Ruhl Libre' => array( array('300','normal','500','700','900'), array('latin-ext','latin','hebrew') ),
      'Freckle Face' => array( array('normal'), array('latin-ext','latin') ),
      'Fredericka the Great' => array( array('normal'), array('latin') ),
      'Fredoka One' => array( array('normal'), array('latin') ),
      'Freehand' => array( array('normal'), array('khmer') ),
      'Fresca' => array( array('normal'), array('latin-ext','latin') ),
      'Frijole' => array( array('normal'), array('latin') ),
      'Fruktur' => array( array('normal'), array('latin-ext','latin') ),
      'Fugaz One' => array( array('normal'), array('latin') ),
      'GFS Didot' => array( array('normal'), array('greek') ),
      'GFS Neohellenic' => array( array('normal','italic','700','700italic'), array('greek') ),
      'Gabriela' => array( array('normal'), array('latin','cyrillic','cyrillic-ext') ),
      'Gaegu' => array( array('300','normal','700'), array('latin','korean') ),
      'Gafata' => array( array('normal'), array('latin-ext','latin') ),
      'Galada' => array( array('normal'), array('bengali','latin') ),
      'Galdeano' => array( array('normal'), array('latin') ),
      'Galindo' => array( array('normal'), array('latin-ext','latin') ),
      'Gamja Flower' => array( array('normal'), array('latin','korean') ),
      'Gentium Basic' => array( array('normal','italic','700','700italic'), array('latin-ext','latin') ),
      'Gentium Book Basic' => array( array('normal','italic','700','700italic'), array('latin-ext','latin') ),
      'Geo' => array( array('normal','italic'), array('latin') ),
      'Geostar' => array( array('normal'), array('latin') ),
      'Geostar Fill' => array( array('normal'), array('latin') ),
      'Germania One' => array( array('normal'), array('latin') ),
      'Gidugu' => array( array('normal'), array('latin','telugu') ),
      'Gilda Display' => array( array('normal'), array('latin-ext','latin') ),
      'Give You Glory' => array( array('normal'), array('latin') ),
      'Glass Antiqua' => array( array('normal'), array('latin-ext','latin') ),
      'Glegoo' => array( array('normal','700'), array('latin-ext','devanagari','latin') ),
      'Gloria Hallelujah' => array( array('normal'), array('latin') ),
      'Goblin One' => array( array('normal'), array('latin') ),
      'Gochi Hand' => array( array('normal'), array('latin') ),
      'Gorditas' => array( array('normal','700'), array('latin') ),
      'Gothic A1' => array( array('100','200','300','normal','500','600','700','800','900'), array('latin','korean') ),
      'Goudy Bookletter 1911' => array( array('normal'), array('latin') ),
      'Graduate' => array( array('normal'), array('latin') ),
      'Grand Hotel' => array( array('normal'), array('latin-ext','latin') ),
      'Gravitas One' => array( array('normal'), array('latin') ),
      'Great Vibes' => array( array('normal'), array('latin-ext','latin') ),
      'Griffy' => array( array('normal'), array('latin-ext','latin') ),
      'Gruppo' => array( array('normal'), array('latin-ext','latin') ),
      'Gudea' => array( array('normal','italic','700'), array('latin-ext','latin') ),
      'Gugi' => array( array('normal'), array('latin','korean') ),
      'Gurajada' => array( array('normal'), array('latin','telugu') ),
      'Habibi' => array( array('normal'), array('latin-ext','latin') ),
      'Halant' => array( array('300','normal','500','600','700'), array('latin-ext','devanagari','latin') ),
      'Hammersmith One' => array( array('normal'), array('latin-ext','latin') ),
      'Hanalei' => array( array('normal'), array('latin-ext','latin') ),
      'Hanalei Fill' => array( array('normal'), array('latin-ext','latin') ),
      'Handlee' => array( array('normal'), array('latin') ),
      'Hanuman' => array( array('normal','700'), array('khmer') ),
      'Happy Monkey' => array( array('normal'), array('latin-ext','latin') ),
      'Harmattan' => array( array('normal'), array('arabic','latin') ),
      'Headland One' => array( array('normal'), array('latin-ext','latin') ),
      'Heebo' => array( array('100','300','normal','500','700','800','900'), array('latin','hebrew') ),
      'Henny Penny' => array( array('normal'), array('latin') ),
      'Herr Von Muellerhoff' => array( array('normal'), array('latin-ext','latin') ),
      'Hi Melody' => array( array('normal'), array('latin','korean') ),
      'Hind' => array( array('300','normal','500','600','700'), array('latin-ext','devanagari','latin') ),
      'Hind Guntur' => array( array('300','normal','500','600','700'), array('latin-ext','latin','telugu') ),
      'Hind Madurai' => array( array('300','normal','500','600','700'), array('latin-ext','tamil','latin') ),
      'Hind Siliguri' => array( array('300','normal','500','600','700'), array('latin-ext','bengali','latin') ),
      'Hind Vadodara' => array( array('300','normal','500','600','700'), array('latin-ext','latin','gujarati') ),
      'Holtwood One SC' => array( array('normal'), array('latin') ),
      'Homemade Apple' => array( array('normal'), array('latin') ),
      'Homenaje' => array( array('normal'), array('latin') ),
      'IBM Plex Mono' => array( array('100','100italic','200','200italic','300','300italic','normal','italic','500','500italic','600','600italic','700','700italic'), array('latin-ext','latin','cyrillic','vietnamese','cyrillic-ext') ),
      'IBM Plex Sans' => array( array('100','100italic','200','200italic','300','300italic','normal','italic','500','500italic','600','600italic','700','700italic'), array('latin-ext','latin','cyrillic','vietnamese','cyrillic-ext') ),
      'IBM Plex Sans Condensed' => array( array('100','100italic','200','200italic','300','300italic','normal','italic','500','500italic','600','600italic','700','700italic'), array('latin-ext','latin','vietnamese') ),
      'IBM Plex Serif' => array( array('100','100italic','200','200italic','300','300italic','normal','italic','500','500italic','600','600italic','700','700italic'), array('latin-ext','latin','cyrillic','vietnamese','cyrillic-ext') ),
      'IM Fell DW Pica' => array( array('normal','italic'), array('latin') ),
      'IM Fell DW Pica SC' => array( array('normal'), array('latin') ),
      'IM Fell Double Pica' => array( array('normal','italic'), array('latin') ),
      'IM Fell Double Pica SC' => array( array('normal'), array('latin') ),
      'IM Fell English' => array( array('normal','italic'), array('latin') ),
      'IM Fell English SC' => array( array('normal'), array('latin') ),
      'IM Fell French Canon' => array( array('normal','italic'), array('latin') ),
      'IM Fell French Canon SC' => array( array('normal'), array('latin') ),
      'IM Fell Great Primer' => array( array('normal','italic'), array('latin') ),
      'IM Fell Great Primer SC' => array( array('normal'), array('latin') ),
      'Iceberg' => array( array('normal'), array('latin') ),
      'Iceland' => array( array('normal'), array('latin') ),
      'Imprima' => array( array('normal'), array('latin-ext','latin') ),
      'Inconsolata' => array( array('normal','700'), array('latin-ext','latin','vietnamese') ),
      'Inder' => array( array('normal'), array('latin-ext','latin') ),
      'Indie Flower' => array( array('normal'), array('latin') ),
      'Inika' => array( array('normal','700'), array('latin-ext','latin') ),
      'Inknut Antiqua' => array( array('300','normal','500','600','700','800','900'), array('latin-ext','devanagari','latin') ),
      'Irish Grover' => array( array('normal'), array('latin') ),
      'Istok Web' => array( array('normal','italic','700','700italic'), array('latin-ext','latin','cyrillic','cyrillic-ext') ),
      'Italiana' => array( array('normal'), array('latin') ),
      'Italianno' => array( array('normal'), array('latin-ext','latin') ),
      'Itim' => array( array('normal'), array('latin-ext','thai','latin','vietnamese') ),
      'Jacques Francois' => array( array('normal'), array('latin') ),
      'Jacques Francois Shadow' => array( array('normal'), array('latin') ),
      'Jaldi' => array( array('normal','700'), array('latin-ext','devanagari','latin') ),
      'Jim Nightshade' => array( array('normal'), array('latin-ext','latin') ),
      'Jockey One' => array( array('normal'), array('latin-ext','latin') ),
      'Jolly Lodger' => array( array('normal'), array('latin-ext','latin') ),
      'Jomhuria' => array( array('normal'), array('latin-ext','arabic','latin') ),
      'Josefin Sans' => array( array('100','100italic','300','300italic','normal','italic','600','600italic','700','700italic'), array('latin-ext','latin','vietnamese') ),
      'Josefin Slab' => array( array('100','100italic','300','300italic','normal','italic','600','600italic','700','700italic'), array('latin') ),
      'Joti One' => array( array('normal'), array('latin-ext','latin') ),
      'Jua' => array( array('normal'), array('latin','korean') ),
      'Judson' => array( array('normal','italic','700'), array('latin-ext','latin','vietnamese') ),
      'Julee' => array( array('normal'), array('latin') ),
      'Julius Sans One' => array( array('normal'), array('latin-ext','latin') ),
      'Junge' => array( array('normal'), array('latin') ),
      'Jura' => array( array('300','normal','500','600','700'), array('latin-ext','greek-ext','greek','latin','cyrillic','vietnamese','cyrillic-ext') ),
      'Just Another Hand' => array( array('normal'), array('latin') ),
      'Just Me Again Down Here' => array( array('normal'), array('latin-ext','latin') ),
      'K2D' => array( array('100','100italic','200','200italic','300','300italic','normal','italic','500','500italic','600','600italic','700','700italic','800','800italic'), array('latin-ext','thai','latin','vietnamese') ),
      'Kadwa' => array( array('normal','700'), array('devanagari','latin') ),
      'Kalam' => array( array('300','normal','700'), array('latin-ext','devanagari','latin') ),
      'Kameron' => array( array('normal','700'), array('latin') ),
      'Kanit' => array( array('100','100italic','200','200italic','300','300italic','normal','italic','500','500italic','600','600italic','700','700italic','800','800italic','900','900italic'), array('latin-ext','thai','latin','vietnamese') ),
      'Kantumruy' => array( array('300','normal','700'), array('khmer') ),
      'Karla' => array( array('normal','italic','700','700italic'), array('latin-ext','latin') ),
      'Karma' => array( array('300','normal','500','600','700'), array('latin-ext','devanagari','latin') ),
      'Katibeh' => array( array('normal'), array('latin-ext','arabic','latin') ),
      'Kaushan Script' => array( array('normal'), array('latin-ext','latin') ),
      'Kavivanar' => array( array('normal'), array('latin-ext','tamil','latin') ),
      'Kavoon' => array( array('normal'), array('latin-ext','latin') ),
      'Kdam Thmor' => array( array('normal'), array('khmer') ),
      'Keania One' => array( array('normal'), array('latin-ext','latin') ),
      'Kelly Slab' => array( array('normal'), array('latin-ext','latin','cyrillic') ),
      'Kenia' => array( array('normal'), array('latin') ),
      'Khand' => array( array('300','normal','500','600','700'), array('latin-ext','devanagari','latin') ),
      'Khmer' => array( array('normal'), array('khmer') ),
      'Khula' => array( array('300','normal','600','700','800'), array('latin-ext','devanagari','latin') ),
      'Kirang Haerang' => array( array('normal'), array('latin','korean') ),
      'Kite One' => array( array('normal'), array('latin') ),
      'Knewave' => array( array('normal'), array('latin-ext','latin') ),
      'KoHo' => array( array('200','200italic','300','300italic','normal','italic','500','500italic','600','600italic','700','700italic'), array('latin-ext','thai','latin','vietnamese') ),
      'Kodchasan' => array( array('200','200italic','300','300italic','normal','italic','500','500italic','600','600italic','700','700italic'), array('latin-ext','thai','latin','vietnamese') ),
      'Kosugi' => array( array('normal'), array('latin','cyrillic','japanese') ),
      'Kosugi Maru' => array( array('normal'), array('latin','cyrillic','japanese') ),
      'Kotta One' => array( array('normal'), array('latin-ext','latin') ),
      'Koulen' => array( array('normal'), array('khmer') ),
      'Kranky' => array( array('normal'), array('latin') ),
      'Kreon' => array( array('300','normal','700'), array('latin') ),
      'Kristi' => array( array('normal'), array('latin') ),
      'Krona One' => array( array('normal'), array('latin-ext','latin') ),
      'Krub' => array( array('200','200italic','300','300italic','normal','italic','500','500italic','600','600italic','700','700italic'), array('latin-ext','thai','latin','vietnamese') ),
      'Kumar One' => array( array('normal'), array('latin-ext','latin','gujarati') ),
      'Kumar One Outline' => array( array('normal'), array('latin-ext','latin','gujarati') ),
      'Kurale' => array( array('normal'), array('latin-ext','devanagari','latin','cyrillic','cyrillic-ext') ),
      'La Belle Aurore' => array( array('normal'), array('latin') ),
      'Laila' => array( array('300','normal','500','600','700'), array('latin-ext','devanagari','latin') ),
      'Lakki Reddy' => array( array('normal'), array('latin','telugu') ),
      'Lalezar' => array( array('normal'), array('latin-ext','arabic','latin','vietnamese') ),
      'Lancelot' => array( array('normal'), array('latin-ext','latin') ),
      'Lateef' => array( array('normal'), array('arabic','latin') ),
      'Lato' => array( array('100','100italic','300','300italic','normal','italic','700','700italic','900','900italic'), array('latin-ext','latin') ),
      'League Script' => array( array('normal'), array('latin') ),
      'Leckerli One' => array( array('normal'), array('latin') ),
      'Ledger' => array( array('normal'), array('latin-ext','latin','cyrillic') ),
      'Lekton' => array( array('normal','italic','700'), array('latin-ext','latin') ),
      'Lemon' => array( array('normal'), array('latin') ),
      'Lemonada' => array( array('300','normal','600','700'), array('latin-ext','arabic','latin','vietnamese') ),
      'Libre Barcode 128' => array( array('normal'), array('latin') ),
      'Libre Barcode 128 Text' => array( array('normal'), array('latin') ),
      'Libre Barcode 39' => array( array('normal'), array('latin') ),
      'Libre Barcode 39 Extended' => array( array('normal'), array('latin') ),
      'Libre Barcode 39 Extended Text' => array( array('normal'), array('latin') ),
      'Libre Barcode 39 Text' => array( array('normal'), array('latin') ),
      'Libre Baskerville' => array( array('normal','italic','700'), array('latin-ext','latin') ),
      'Libre Franklin' => array( array('100','100italic','200','200italic','300','300italic','normal','italic','500','500italic','600','600italic','700','700italic','800','800italic','900','900italic'), array('latin-ext','latin') ),
      'Life Savers' => array( array('normal','700'), array('latin-ext','latin') ),
      'Lilita One' => array( array('normal'), array('latin-ext','latin') ),
      'Lily Script One' => array( array('normal'), array('latin-ext','latin') ),
      'Limelight' => array( array('normal'), array('latin-ext','latin') ),
      'Linden Hill' => array( array('normal','italic'), array('latin') ),
      'Lobster' => array( array('normal'), array('latin-ext','latin','cyrillic','vietnamese','cyrillic-ext') ),
      'Lobster Two' => array( array('normal','italic','700','700italic'), array('latin') ),
      'Londrina Outline' => array( array('normal'), array('latin') ),
      'Londrina Shadow' => array( array('normal'), array('latin') ),
      'Londrina Sketch' => array( array('normal'), array('latin') ),
      'Londrina Solid' => array( array('100','300','normal','900'), array('latin') ),
      'Lora' => array( array('normal','italic','700','700italic'), array('latin-ext','latin','cyrillic','vietnamese','cyrillic-ext') ),
      'Love Ya Like A Sister' => array( array('normal'), array('latin') ),
      'Loved by the King' => array( array('normal'), array('latin') ),
      'Lovers Quarrel' => array( array('normal'), array('latin-ext','latin') ),
      'Luckiest Guy' => array( array('normal'), array('latin') ),
      'Lusitana' => array( array('normal','700'), array('latin') ),
      'Lustria' => array( array('normal'), array('latin') ),
      'M PLUS 1p' => array( array('100','300','normal','500','700','800','900'), array('latin-ext','greek-ext','greek','latin','cyrillic','vietnamese','hebrew','cyrillic-ext','japanese') ),
      'M PLUS Rounded 1c' => array( array('100','300','normal','500','700','800','900'), array('latin-ext','greek-ext','greek','latin','cyrillic','vietnamese','hebrew','cyrillic-ext','japanese') ),
      'Macondo' => array( array('normal'), array('latin') ),
      'Macondo Swash Caps' => array( array('normal'), array('latin') ),
      'Mada' => array( array('200','300','normal','500','600','700','900'), array('arabic','latin') ),
      'Magra' => array( array('normal','700'), array('latin-ext','latin') ),
      'Maiden Orange' => array( array('normal'), array('latin') ),
      'Maitree' => array( array('200','300','normal','500','600','700'), array('latin-ext','thai','latin','vietnamese') ),
      'Mako' => array( array('normal'), array('latin') ),
      'Mali' => array( array('200','200italic','300','300italic','normal','italic','500','500italic','600','600italic','700','700italic'), array('latin-ext','thai','latin','vietnamese') ),
      'Mallanna' => array( array('normal'), array('latin','telugu') ),
      'Mandali' => array( array('normal'), array('latin','telugu') ),
      'Manuale' => array( array('normal','italic','500','500italic','600','600italic','700','700italic'), array('latin-ext','latin','vietnamese') ),
      'Marcellus' => array( array('normal'), array('latin-ext','latin') ),
      'Marcellus SC' => array( array('normal'), array('latin-ext','latin') ),
      'Marck Script' => array( array('normal'), array('latin-ext','latin','cyrillic') ),
      'Margarine' => array( array('normal'), array('latin-ext','latin') ),
      'Markazi Text' => array( array('normal','500','600','700'), array('latin-ext','arabic','latin','vietnamese') ),
      'Marko One' => array( array('normal'), array('latin') ),
      'Marmelad' => array( array('normal'), array('latin-ext','latin','cyrillic') ),
      'Martel' => array( array('200','300','normal','600','700','800','900'), array('latin-ext','devanagari','latin') ),
      'Martel Sans' => array( array('200','300','normal','600','700','800','900'), array('latin-ext','devanagari','latin') ),
      'Marvel' => array( array('normal','italic','700','700italic'), array('latin') ),
      'Mate' => array( array('normal','italic'), array('latin') ),
      'Mate SC' => array( array('normal'), array('latin') ),
      'Maven Pro' => array( array('normal','500','700','900'), array('latin-ext','latin','vietnamese') ),
      'McLaren' => array( array('normal'), array('latin-ext','latin') ),
      'Meddon' => array( array('normal'), array('latin') ),
      'MedievalSharp' => array( array('normal'), array('latin-ext','latin') ),
      'Medula One' => array( array('normal'), array('latin') ),
      'Meera Inimai' => array( array('normal'), array('tamil','latin') ),
      'Megrim' => array( array('normal'), array('latin') ),
      'Meie Script' => array( array('normal'), array('latin-ext','latin') ),
      'Merienda' => array( array('normal','700'), array('latin-ext','latin') ),
      'Merienda One' => array( array('normal'), array('latin') ),
      'Merriweather' => array( array('300','300italic','normal','italic','700','700italic','900','900italic'), array('latin-ext','latin','cyrillic','vietnamese','cyrillic-ext') ),
      'Merriweather Sans' => array( array('300','300italic','normal','italic','700','700italic','800','800italic'), array('latin-ext','latin') ),
      'Metal' => array( array('normal'), array('khmer') ),
      'Metal Mania' => array( array('normal'), array('latin-ext','latin') ),
      'Metamorphous' => array( array('normal'), array('latin-ext','latin') ),
      'Metrophobic' => array( array('normal'), array('latin') ),
      'Michroma' => array( array('normal'), array('latin') ),
      'Milonga' => array( array('normal'), array('latin-ext','latin') ),
      'Miltonian' => array( array('normal'), array('latin') ),
      'Miltonian Tattoo' => array( array('normal'), array('latin') ),
      'Mina' => array( array('normal','700'), array('latin-ext','bengali','latin') ),
      'Miniver' => array( array('normal'), array('latin') ),
      'Miriam Libre' => array( array('normal','700'), array('latin-ext','latin','hebrew') ),
      'Mirza' => array( array('normal','500','600','700'), array('latin-ext','arabic','latin') ),
      'Miss Fajardose' => array( array('normal'), array('latin-ext','latin') ),
      'Mitr' => array( array('200','300','normal','500','600','700'), array('latin-ext','thai','latin','vietnamese') ),
      'Modak' => array( array('normal'), array('latin-ext','devanagari','latin') ),
      'Modern Antiqua' => array( array('normal'), array('latin-ext','latin') ),
      'Mogra' => array( array('normal'), array('latin-ext','latin','gujarati') ),
      'Molengo' => array( array('normal'), array('latin-ext','latin') ),
      'Molle' => array( array('italic'), array('latin-ext','latin') ),
      'Monda' => array( array('normal','700'), array('latin-ext','latin') ),
      'Monofett' => array( array('normal'), array('latin') ),
      'Monoton' => array( array('normal'), array('latin') ),
      'Monsieur La Doulaise' => array( array('normal'), array('latin-ext','latin') ),
      'Montaga' => array( array('normal'), array('latin') ),
      'Montez' => array( array('normal'), array('latin') ),
      'Montserrat' => array( array('100','100italic','200','200italic','300','300italic','normal','italic','500','500italic','600','600italic','700','700italic','800','800italic','900','900italic'), array('latin-ext','latin','cyrillic','vietnamese','cyrillic-ext') ),
      'Montserrat Alternates' => array( array('100','100italic','200','200italic','300','300italic','normal','italic','500','500italic','600','600italic','700','700italic','800','800italic','900','900italic'), array('latin-ext','latin','cyrillic','vietnamese','cyrillic-ext') ),
      'Montserrat Subrayada' => array( array('normal','700'), array('latin') ),
      'Moul' => array( array('normal'), array('khmer') ),
      'Moulpali' => array( array('normal'), array('khmer') ),
      'Mountains of Christmas' => array( array('normal','700'), array('latin') ),
      'Mouse Memoirs' => array( array('normal'), array('latin-ext','latin') ),
      'Mr Bedfort' => array( array('normal'), array('latin-ext','latin') ),
      'Mr Dafoe' => array( array('normal'), array('latin-ext','latin') ),
      'Mr De Haviland' => array( array('normal'), array('latin-ext','latin') ),
      'Mrs Saint Delafield' => array( array('normal'), array('latin-ext','latin') ),
      'Mrs Sheppards' => array( array('normal'), array('latin-ext','latin') ),
      'Mukta' => array( array('200','300','normal','500','600','700','800'), array('latin-ext','devanagari','latin') ),
      'Mukta Mahee' => array( array('200','300','normal','500','600','700','800'), array('latin-ext','latin','gurmukhi') ),
      'Mukta Malar' => array( array('200','300','normal','500','600','700','800'), array('latin-ext','tamil','latin') ),
      'Mukta Vaani' => array( array('200','300','normal','500','600','700','800'), array('latin-ext','latin','gujarati') ),
      'Muli' => array( array('200','200italic','300','300italic','normal','italic','600','600italic','700','700italic','800','800italic','900','900italic'), array('latin-ext','latin','vietnamese') ),
      'Mystery Quest' => array( array('normal'), array('latin-ext','latin') ),
      'NTR' => array( array('normal'), array('latin','telugu') ),
      'Nanum Brush Script' => array( array('normal'), array('latin','korean') ),
      'Nanum Gothic' => array( array('normal','700','800'), array('latin','korean') ),
      'Nanum Gothic Coding' => array( array('normal','700'), array('latin','korean') ),
      'Nanum Myeongjo' => array( array('normal','700','800'), array('latin','korean') ),
      'Nanum Pen Script' => array( array('normal'), array('latin','korean') ),
      'Neucha' => array( array('normal'), array('latin','cyrillic') ),
      'Neuton' => array( array('200','300','normal','italic','700','800'), array('latin-ext','latin') ),
      'New Rocker' => array( array('normal'), array('latin-ext','latin') ),
      'News Cycle' => array( array('normal','700'), array('latin-ext','latin') ),
      'Niconne' => array( array('normal'), array('latin-ext','latin') ),
      'Niramit' => array( array('200','200italic','300','300italic','normal','italic','500','500italic','600','600italic','700','700italic'), array('latin-ext','thai','latin','vietnamese') ),
      'Nixie One' => array( array('normal'), array('latin') ),
      'Nobile' => array( array('normal','italic','500','500italic','700','700italic'), array('latin-ext','latin') ),
      'Nokora' => array( array('normal','700'), array('khmer') ),
      'Norican' => array( array('normal'), array('latin-ext','latin') ),
      'Nosifer' => array( array('normal'), array('latin-ext','latin') ),
      'Notable' => array( array('normal'), array('latin') ),
      'Nothing You Could Do' => array( array('normal'), array('latin') ),
      'Noticia Text' => array( array('normal','italic','700','700italic'), array('latin-ext','latin','vietnamese') ),
      'Noto Sans' => array( array('normal','italic','700','700italic'), array('latin-ext','greek-ext','devanagari','greek','latin','cyrillic','vietnamese','cyrillic-ext') ),
      'Noto Sans JP' => array( array('100','300','normal','500','700','900'), array('latin','japanese') ),
      'Noto Sans KR' => array( array('100','300','normal','500','700','900'), array('latin','korean') ),
      'Noto Sans SC' => array( array('100','300','normal','500','700','900'), array('chinese-simplified','latin','cyrillic','vietnamese','japanese') ),
      'Noto Sans TC' => array( array('100','300','normal','500','700','900'), array('chinese-traditional','latin','japanese') ),
      'Noto Serif' => array( array('normal','italic','700','700italic'), array('latin-ext','greek-ext','greek','latin','cyrillic','vietnamese','cyrillic-ext') ),
      'Noto Serif JP' => array( array('200','300','normal','500','600','700','900'), array('latin','japanese') ),
      'Noto Serif KR' => array( array('200','300','normal','500','600','700','900'), array('latin','korean') ),
      'Nova Cut' => array( array('normal'), array('latin') ),
      'Nova Flat' => array( array('normal'), array('latin') ),
      'Nova Mono' => array( array('normal'), array('greek','latin') ),
      'Nova Oval' => array( array('normal'), array('latin') ),
      'Nova Round' => array( array('normal'), array('latin') ),
      'Nova Script' => array( array('normal'), array('latin') ),
      'Nova Slim' => array( array('normal'), array('latin') ),
      'Nova Square' => array( array('normal'), array('latin') ),
      'Numans' => array( array('normal'), array('latin') ),
      'Nunito' => array( array('200','200italic','300','300italic','normal','italic','600','600italic','700','700italic','800','800italic','900','900italic'), array('latin-ext','latin','vietnamese') ),
      'Nunito Sans' => array( array('200','200italic','300','300italic','normal','italic','600','600italic','700','700italic','800','800italic','900','900italic'), array('latin-ext','latin','vietnamese') ),
      'Odor Mean Chey' => array( array('normal'), array('khmer') ),
      'Offside' => array( array('normal'), array('latin') ),
      'Old Standard TT' => array( array('normal','italic','700'), array('latin-ext','latin','cyrillic','vietnamese','cyrillic-ext') ),
      'Oldenburg' => array( array('normal'), array('latin-ext','latin') ),
      'Oleo Script' => array( array('normal','700'), array('latin-ext','latin') ),
      'Oleo Script Swash Caps' => array( array('normal','700'), array('latin-ext','latin') ),
      'Open Sans' => array( array('300','300italic','normal','italic','600','600italic','700','700italic','800','800italic'), array('latin-ext','greek-ext','greek','latin','cyrillic','vietnamese','cyrillic-ext') ),
      'Open Sans Condensed' => array( array('300','300italic','700'), array('latin-ext','greek-ext','greek','latin','cyrillic','vietnamese','cyrillic-ext') ),
      'Oranienbaum' => array( array('normal'), array('latin-ext','latin','cyrillic','cyrillic-ext') ),
      'Orbitron' => array( array('normal','500','700','900'), array('latin') ),
      'Oregano' => array( array('normal','italic'), array('latin-ext','latin') ),
      'Orienta' => array( array('normal'), array('latin-ext','latin') ),
      'Original Surfer' => array( array('normal'), array('latin') ),
      'Oswald' => array( array('200','300','normal','500','600','700'), array('latin-ext','latin','cyrillic','vietnamese') ),
      'Over the Rainbow' => array( array('normal'), array('latin') ),
      'Overlock' => array( array('normal','italic','700','700italic','900','900italic'), array('latin-ext','latin') ),
      'Overlock SC' => array( array('normal'), array('latin-ext','latin') ),
      'Overpass' => array( array('100','100italic','200','200italic','300','300italic','normal','italic','600','600italic','700','700italic','800','800italic','900','900italic'), array('latin-ext','latin') ),
      'Overpass Mono' => array( array('300','normal','600','700'), array('latin-ext','latin') ),
      'Ovo' => array( array('normal'), array('latin') ),
      'Oxygen' => array( array('300','normal','700'), array('latin-ext','latin') ),
      'Oxygen Mono' => array( array('normal'), array('latin-ext','latin') ),
      'PT Mono' => array( array('normal'), array('latin-ext','latin','cyrillic','cyrillic-ext') ),
      'PT Sans' => array( array('normal','italic','700','700italic'), array('latin-ext','latin','cyrillic','cyrillic-ext') ),
      'PT Sans Caption' => array( array('normal','700'), array('latin-ext','latin','cyrillic','cyrillic-ext') ),
      'PT Sans Narrow' => array( array('normal','700'), array('latin-ext','latin','cyrillic','cyrillic-ext') ),
      'PT Serif' => array( array('normal','italic','700','700italic'), array('latin-ext','latin','cyrillic','cyrillic-ext') ),
      'PT Serif Caption' => array( array('normal','italic'), array('latin-ext','latin','cyrillic','cyrillic-ext') ),
      'Pacifico' => array( array('normal'), array('latin-ext','latin','cyrillic','vietnamese') ),
      'Padauk' => array( array('normal','700'), array('latin','myanmar') ),
      'Palanquin' => array( array('100','200','300','normal','500','600','700'), array('latin-ext','devanagari','latin') ),
      'Palanquin Dark' => array( array('normal','500','600','700'), array('latin-ext','devanagari','latin') ),
      'Pangolin' => array( array('normal'), array('latin-ext','latin','cyrillic','vietnamese','cyrillic-ext') ),
      'Paprika' => array( array('normal'), array('latin') ),
      'Parisienne' => array( array('normal'), array('latin-ext','latin') ),
      'Passero One' => array( array('normal'), array('latin-ext','latin') ),
      'Passion One' => array( array('normal','700','900'), array('latin-ext','latin') ),
      'Pathway Gothic One' => array( array('normal'), array('latin-ext','latin') ),
      'Patrick Hand' => array( array('normal'), array('latin-ext','latin','vietnamese') ),
      'Patrick Hand SC' => array( array('normal'), array('latin-ext','latin','vietnamese') ),
      'Pattaya' => array( array('normal'), array('latin-ext','thai','latin','cyrillic','vietnamese') ),
      'Patua One' => array( array('normal'), array('latin') ),
      'Pavanam' => array( array('normal'), array('latin-ext','tamil','latin') ),
      'Paytone One' => array( array('normal'), array('latin-ext','latin','vietnamese') ),
      'Peddana' => array( array('normal'), array('latin','telugu') ),
      'Peralta' => array( array('normal'), array('latin-ext','latin') ),
      'Permanent Marker' => array( array('normal'), array('latin') ),
      'Petit Formal Script' => array( array('normal'), array('latin-ext','latin') ),
      'Petrona' => array( array('normal'), array('latin') ),
      'Philosopher' => array( array('normal','italic','700','700italic'), array('latin','cyrillic','vietnamese','cyrillic-ext') ),
      'Piedra' => array( array('normal'), array('latin-ext','latin') ),
      'Pinyon Script' => array( array('normal'), array('latin') ),
      'Pirata One' => array( array('normal'), array('latin-ext','latin') ),
      'Plaster' => array( array('normal'), array('latin-ext','latin') ),
      'Play' => array( array('normal','700'), array('latin-ext','greek','latin','cyrillic','vietnamese','cyrillic-ext') ),
      'Playball' => array( array('normal'), array('latin-ext','latin') ),
      'Playfair Display' => array( array('normal','italic','700','700italic','900','900italic'), array('latin-ext','latin','cyrillic','vietnamese') ),
      'Playfair Display SC' => array( array('normal','italic','700','700italic','900','900italic'), array('latin-ext','latin','cyrillic','vietnamese') ),
      'Podkova' => array( array('normal','500','600','700','800'), array('latin-ext','latin','cyrillic','vietnamese','cyrillic-ext') ),
      'Poiret One' => array( array('normal'), array('latin-ext','latin','cyrillic') ),
      'Poller One' => array( array('normal'), array('latin') ),
      'Poly' => array( array('normal','italic'), array('latin') ),
      'Pompiere' => array( array('normal'), array('latin') ),
      'Pontano Sans' => array( array('normal'), array('latin-ext','latin') ),
      'Poor Story' => array( array('normal'), array('latin','korean') ),
      'Poppins' => array( array('100','100italic','200','200italic','300','300italic','normal','italic','500','500italic','600','600italic','700','700italic','800','800italic','900','900italic'), array('latin-ext','devanagari','latin') ),
      'Port Lligat Sans' => array( array('normal'), array('latin') ),
      'Port Lligat Slab' => array( array('normal'), array('latin') ),
      'Pragati Narrow' => array( array('normal','700'), array('latin-ext','devanagari','latin') ),
      'Prata' => array( array('normal'), array('latin','cyrillic','vietnamese','cyrillic-ext') ),
      'Preahvihear' => array( array('normal'), array('khmer') ),
      'Press Start 2P' => array( array('normal'), array('latin-ext','greek','latin','cyrillic','cyrillic-ext') ),
      'Pridi' => array( array('200','300','normal','500','600','700'), array('latin-ext','thai','latin','vietnamese') ),
      'Princess Sofia' => array( array('normal'), array('latin-ext','latin') ),
      'Prociono' => array( array('normal'), array('latin') ),
      'Prompt' => array( array('100','100italic','200','200italic','300','300italic','normal','italic','500','500italic','600','600italic','700','700italic','800','800italic','900','900italic'), array('latin-ext','thai','latin','vietnamese') ),
      'Prosto One' => array( array('normal'), array('latin-ext','latin','cyrillic') ),
      'Proza Libre' => array( array('normal','italic','500','500italic','600','600italic','700','700italic','800','800italic'), array('latin-ext','latin') ),
      'Puritan' => array( array('normal','italic','700','700italic'), array('latin') ),
      'Purple Purse' => array( array('normal'), array('latin-ext','latin') ),
      'Quando' => array( array('normal'), array('latin-ext','latin') ),
      'Quantico' => array( array('normal','italic','700','700italic'), array('latin') ),
      'Quattrocento' => array( array('normal','700'), array('latin-ext','latin') ),
      'Quattrocento Sans' => array( array('normal','italic','700','700italic'), array('latin-ext','latin') ),
      'Questrial' => array( array('normal'), array('latin') ),
      'Quicksand' => array( array('300','normal','500','700'), array('latin-ext','latin','vietnamese') ),
      'Quintessential' => array( array('normal'), array('latin-ext','latin') ),
      'Qwigley' => array( array('normal'), array('latin-ext','latin') ),
      'Racing Sans One' => array( array('normal'), array('latin-ext','latin') ),
      'Radley' => array( array('normal','italic'), array('latin-ext','latin') ),
      'Rajdhani' => array( array('300','normal','500','600','700'), array('latin-ext','devanagari','latin') ),
      'Rakkas' => array( array('normal'), array('latin-ext','arabic','latin') ),
      'Raleway' => array( array('100','100italic','200','200italic','300','300italic','normal','italic','500','500italic','600','600italic','700','700italic','800','800italic','900','900italic'), array('latin-ext','latin') ),
      'Raleway Dots' => array( array('normal'), array('latin-ext','latin') ),
      'Ramabhadra' => array( array('normal'), array('latin','telugu') ),
      'Ramaraja' => array( array('normal'), array('latin','telugu') ),
      'Rambla' => array( array('normal','italic','700','700italic'), array('latin-ext','latin') ),
      'Rammetto One' => array( array('normal'), array('latin-ext','latin') ),
      'Ranchers' => array( array('normal'), array('latin-ext','latin') ),
      'Rancho' => array( array('normal'), array('latin') ),
      'Ranga' => array( array('normal','700'), array('latin-ext','devanagari','latin') ),
      'Rasa' => array( array('300','normal','500','600','700'), array('latin-ext','latin','gujarati') ),
      'Rationale' => array( array('normal'), array('latin') ),
      'Ravi Prakash' => array( array('normal'), array('latin','telugu') ),
      'Redressed' => array( array('normal'), array('latin') ),
      'Reem Kufi' => array( array('normal'), array('arabic','latin') ),
      'Reenie Beanie' => array( array('normal'), array('latin') ),
      'Revalia' => array( array('normal'), array('latin-ext','latin') ),
      'Rhodium Libre' => array( array('normal'), array('latin-ext','devanagari','latin') ),
      'Ribeye' => array( array('normal'), array('latin-ext','latin') ),
      'Ribeye Marrow' => array( array('normal'), array('latin-ext','latin') ),
      'Righteous' => array( array('normal'), array('latin-ext','latin') ),
      'Risque' => array( array('normal'), array('latin-ext','latin') ),
      'Roboto' => array( array('100','100italic','300','300italic','normal','italic','500','500italic','700','700italic','900','900italic'), array('latin-ext','greek-ext','greek','latin','cyrillic','vietnamese','cyrillic-ext') ),
      'Roboto Condensed' => array( array('300','300italic','normal','italic','700','700italic'), array('latin-ext','greek-ext','greek','latin','cyrillic','vietnamese','cyrillic-ext') ),
      'Roboto Mono' => array( array('100','100italic','300','300italic','normal','italic','500','500italic','700','700italic'), array('latin-ext','greek-ext','greek','latin','cyrillic','vietnamese','cyrillic-ext') ),
      'Roboto Slab' => array( array('100','300','normal','700'), array('latin-ext','greek-ext','greek','latin','cyrillic','vietnamese','cyrillic-ext') ),
      'Rochester' => array( array('normal'), array('latin') ),
      'Rock Salt' => array( array('normal'), array('latin') ),
      'Rokkitt' => array( array('100','200','300','normal','500','600','700','800','900'), array('latin-ext','latin','vietnamese') ),
      'Romanesco' => array( array('normal'), array('latin-ext','latin') ),
      'Ropa Sans' => array( array('normal','italic'), array('latin-ext','latin') ),
      'Rosario' => array( array('normal','italic','700','700italic'), array('latin') ),
      'Rosarivo' => array( array('normal','italic'), array('latin-ext','latin') ),
      'Rouge Script' => array( array('normal'), array('latin') ),
      'Rozha One' => array( array('normal'), array('latin-ext','devanagari','latin') ),
      'Rubik' => array( array('300','300italic','normal','italic','500','500italic','700','700italic','900','900italic'), array('latin-ext','latin','cyrillic','hebrew') ),
      'Rubik Mono One' => array( array('normal'), array('latin-ext','latin','cyrillic') ),
      'Ruda' => array( array('normal','700','900'), array('latin-ext','latin') ),
      'Rufina' => array( array('normal','700'), array('latin-ext','latin') ),
      'Ruge Boogie' => array( array('normal'), array('latin-ext','latin') ),
      'Ruluko' => array( array('normal'), array('latin-ext','latin') ),
      'Rum Raisin' => array( array('normal'), array('latin-ext','latin') ),
      'Ruslan Display' => array( array('normal'), array('latin-ext','latin','cyrillic') ),
      'Russo One' => array( array('normal'), array('latin-ext','latin','cyrillic') ),
      'Ruthie' => array( array('normal'), array('latin-ext','latin') ),
      'Rye' => array( array('normal'), array('latin-ext','latin') ),
      'Sacramento' => array( array('normal'), array('latin-ext','latin') ),
      'Sahitya' => array( array('normal','700'), array('devanagari','latin') ),
      'Sail' => array( array('normal'), array('latin-ext','latin') ),
      'Saira' => array( array('100','200','300','normal','500','600','700','800','900'), array('latin-ext','latin','vietnamese') ),
      'Saira Condensed' => array( array('100','200','300','normal','500','600','700','800','900'), array('latin-ext','latin','vietnamese') ),
      'Saira Extra Condensed' => array( array('100','200','300','normal','500','600','700','800','900'), array('latin-ext','latin','vietnamese') ),
      'Saira Semi Condensed' => array( array('100','200','300','normal','500','600','700','800','900'), array('latin-ext','latin','vietnamese') ),
      'Salsa' => array( array('normal'), array('latin') ),
      'Sanchez' => array( array('normal','italic'), array('latin-ext','latin') ),
      'Sancreek' => array( array('normal'), array('latin-ext','latin') ),
      'Sansita' => array( array('normal','italic','700','700italic','800','800italic','900','900italic'), array('latin-ext','latin') ),
      'Sarala' => array( array('normal','700'), array('latin-ext','devanagari','latin') ),
      'Sarina' => array( array('normal'), array('latin-ext','latin') ),
      'Sarpanch' => array( array('normal','500','600','700','800','900'), array('latin-ext','devanagari','latin') ),
      'Satisfy' => array( array('normal'), array('latin') ),
      'Sawarabi Gothic' => array( array('normal'), array('latin-ext','latin','cyrillic','vietnamese','japanese') ),
      'Sawarabi Mincho' => array( array('normal'), array('latin-ext','latin','japanese') ),
      'Scada' => array( array('normal','italic','700','700italic'), array('latin-ext','latin','cyrillic','cyrillic-ext') ),
      'Scheherazade' => array( array('normal','700'), array('arabic','latin') ),
      'Schoolbell' => array( array('normal'), array('latin') ),
      'Scope One' => array( array('normal'), array('latin-ext','latin') ),
      'Seaweed Script' => array( array('normal'), array('latin-ext','latin') ),
      'Secular One' => array( array('normal'), array('latin-ext','latin','hebrew') ),
      'Sedgwick Ave' => array( array('normal'), array('latin-ext','latin','vietnamese') ),
      'Sedgwick Ave Display' => array( array('normal'), array('latin-ext','latin','vietnamese') ),
      'Sevillana' => array( array('normal'), array('latin-ext','latin') ),
      'Seymour One' => array( array('normal'), array('latin-ext','latin','cyrillic') ),
      'Shadows Into Light' => array( array('normal'), array('latin') ),
      'Shadows Into Light Two' => array( array('normal'), array('latin-ext','latin') ),
      'Shanti' => array( array('normal'), array('latin') ),
      'Share' => array( array('normal','italic','700','700italic'), array('latin-ext','latin') ),
      'Share Tech' => array( array('normal'), array('latin') ),
      'Share Tech Mono' => array( array('normal'), array('latin') ),
      'Shojumaru' => array( array('normal'), array('latin-ext','latin') ),
      'Short Stack' => array( array('normal'), array('latin') ),
      'Shrikhand' => array( array('normal'), array('latin-ext','latin','gujarati') ),
      'Siemreap' => array( array('normal'), array('khmer') ),
      'Sigmar One' => array( array('normal'), array('latin-ext','latin','vietnamese') ),
      'Signika' => array( array('300','normal','600','700'), array('latin-ext','latin') ),
      'Signika Negative' => array( array('300','normal','600','700'), array('latin-ext','latin') ),
      'Simonetta' => array( array('normal','italic','900','900italic'), array('latin-ext','latin') ),
      'Sintony' => array( array('normal','700'), array('latin-ext','latin') ),
      'Sirin Stencil' => array( array('normal'), array('latin') ),
      'Six Caps' => array( array('normal'), array('latin') ),
      'Skranji' => array( array('normal','700'), array('latin-ext','latin') ),
      'Slabo 13px' => array( array('normal'), array('latin-ext','latin') ),
      'Slabo 27px' => array( array('normal'), array('latin-ext','latin') ),
      'Slackey' => array( array('normal'), array('latin') ),
      'Smokum' => array( array('normal'), array('latin') ),
      'Smythe' => array( array('normal'), array('latin') ),
      'Sniglet' => array( array('normal','800'), array('latin-ext','latin') ),
      'Snippet' => array( array('normal'), array('latin') ),
      'Snowburst One' => array( array('normal'), array('latin-ext','latin') ),
      'Sofadi One' => array( array('normal'), array('latin') ),
      'Sofia' => array( array('normal'), array('latin') ),
      'Song Myung' => array( array('normal'), array('latin','korean') ),
      'Sonsie One' => array( array('normal'), array('latin-ext','latin') ),
      'Sorts Mill Goudy' => array( array('normal','italic'), array('latin-ext','latin') ),
      'Source Code Pro' => array( array('200','300','normal','500','600','700','900'), array('latin-ext','latin') ),
      'Source Sans Pro' => array( array('200','200italic','300','300italic','normal','italic','600','600italic','700','700italic','900','900italic'), array('latin-ext','greek-ext','greek','latin','cyrillic','vietnamese','cyrillic-ext') ),
      'Source Serif Pro' => array( array('normal','600','700'), array('latin-ext','latin') ),
      'Space Mono' => array( array('normal','italic','700','700italic'), array('latin-ext','latin','vietnamese') ),
      'Special Elite' => array( array('normal'), array('latin') ),
      'Spectral' => array( array('200','200italic','300','300italic','normal','italic','500','500italic','600','600italic','700','700italic','800','800italic'), array('latin-ext','latin','cyrillic','vietnamese') ),
      'Spectral SC' => array( array('200','200italic','300','300italic','normal','italic','500','500italic','600','600italic','700','700italic','800','800italic'), array('latin-ext','latin','cyrillic','vietnamese') ),
      'Spicy Rice' => array( array('normal'), array('latin') ),
      'Spinnaker' => array( array('normal'), array('latin-ext','latin') ),
      'Spirax' => array( array('normal'), array('latin') ),
      'Squada One' => array( array('normal'), array('latin') ),
      'Sree Krushnadevaraya' => array( array('normal'), array('latin','telugu') ),
      'Sriracha' => array( array('normal'), array('latin-ext','thai','latin','vietnamese') ),
      'Srisakdi' => array( array('normal','700'), array('latin-ext','thai','latin','vietnamese') ),
      'Stalemate' => array( array('normal'), array('latin-ext','latin') ),
      'Stalinist One' => array( array('normal'), array('latin-ext','latin','cyrillic') ),
      'Stardos Stencil' => array( array('normal','700'), array('latin') ),
      'Stint Ultra Condensed' => array( array('normal'), array('latin-ext','latin') ),
      'Stint Ultra Expanded' => array( array('normal'), array('latin-ext','latin') ),
      'Stoke' => array( array('300','normal'), array('latin-ext','latin') ),
      'Strait' => array( array('normal'), array('latin') ),
      'Stylish' => array( array('normal'), array('latin','korean') ),
      'Sue Ellen Francisco' => array( array('normal'), array('latin') ),
      'Suez One' => array( array('normal'), array('latin-ext','latin','hebrew') ),
      'Sumana' => array( array('normal','700'), array('latin-ext','devanagari','latin') ),
      'Sunflower' => array( array('300','500','700'), array('latin','korean') ),
      'Sunshiney' => array( array('normal'), array('latin') ),
      'Supermercado One' => array( array('normal'), array('latin') ),
      'Sura' => array( array('normal','700'), array('latin-ext','devanagari','latin') ),
      'Suranna' => array( array('normal'), array('latin','telugu') ),
      'Suravaram' => array( array('normal'), array('latin','telugu') ),
      'Suwannaphum' => array( array('normal'), array('khmer') ),
      'Swanky and Moo Moo' => array( array('normal'), array('latin') ),
      'Syncopate' => array( array('normal','700'), array('latin') ),
      'Tajawal' => array( array('200','300','normal','500','700','800','900'), array('arabic','latin') ),
      'Tangerine' => array( array('normal','700'), array('latin') ),
      'Taprom' => array( array('normal'), array('khmer') ),
      'Tauri' => array( array('normal'), array('latin-ext','latin') ),
      'Taviraj' => array( array('100','100italic','200','200italic','300','300italic','normal','italic','500','500italic','600','600italic','700','700italic','800','800italic','900','900italic'), array('latin-ext','thai','latin','vietnamese') ),
      'Teko' => array( array('300','normal','500','600','700'), array('latin-ext','devanagari','latin') ),
      'Telex' => array( array('normal'), array('latin-ext','latin') ),
      'Tenali Ramakrishna' => array( array('normal'), array('latin','telugu') ),
      'Tenor Sans' => array( array('normal'), array('latin-ext','latin','cyrillic') ),
      'Text Me One' => array( array('normal'), array('latin-ext','latin') ),
      'The Girl Next Door' => array( array('normal'), array('latin') ),
      'Tienne' => array( array('normal','700','900'), array('latin') ),
      'Tillana' => array( array('normal','500','600','700','800'), array('latin-ext','devanagari','latin') ),
      'Timmana' => array( array('normal'), array('latin','telugu') ),
      'Tinos' => array( array('normal','italic','700','700italic'), array('latin-ext','greek-ext','greek','latin','cyrillic','vietnamese','hebrew','cyrillic-ext') ),
      'Titan One' => array( array('normal'), array('latin-ext','latin') ),
      'Titillium Web' => array( array('200','200italic','300','300italic','normal','italic','600','600italic','700','700italic','900'), array('latin-ext','latin') ),
      'Trade Winds' => array( array('normal'), array('latin') ),
      'Trirong' => array( array('100','100italic','200','200italic','300','300italic','normal','italic','500','500italic','600','600italic','700','700italic','800','800italic','900','900italic'), array('latin-ext','thai','latin','vietnamese') ),
      'Trocchi' => array( array('normal'), array('latin-ext','latin') ),
      'Trochut' => array( array('normal','italic','700'), array('latin') ),
      'Trykker' => array( array('normal'), array('latin-ext','latin') ),
      'Tulpen One' => array( array('normal'), array('latin') ),
      'Ubuntu' => array( array('300','300italic','normal','italic','500','500italic','700','700italic'), array('latin-ext','greek-ext','greek','latin','cyrillic','cyrillic-ext') ),
      'Ubuntu Condensed' => array( array('normal'), array('latin-ext','greek-ext','greek','latin','cyrillic','cyrillic-ext') ),
      'Ubuntu Mono' => array( array('normal','italic','700','700italic'), array('latin-ext','greek-ext','greek','latin','cyrillic','cyrillic-ext') ),
      'Ultra' => array( array('normal'), array('latin') ),
      'Uncial Antiqua' => array( array('normal'), array('latin') ),
      'Underdog' => array( array('normal'), array('latin-ext','latin','cyrillic') ),
      'Unica One' => array( array('normal'), array('latin-ext','latin') ),
      'UnifrakturCook' => array( array('700'), array('latin') ),
      'UnifrakturMaguntia' => array( array('normal'), array('latin') ),
      'Unkempt' => array( array('normal','700'), array('latin') ),
      'Unlock' => array( array('normal'), array('latin') ),
      'Unna' => array( array('normal','italic','700','700italic'), array('latin-ext','latin') ),
      'VT323' => array( array('normal'), array('latin-ext','latin','vietnamese') ),
      'Vampiro One' => array( array('normal'), array('latin-ext','latin') ),
      'Varela' => array( array('normal'), array('latin-ext','latin') ),
      'Varela Round' => array( array('normal'), array('latin-ext','latin','vietnamese','hebrew') ),
      'Vast Shadow' => array( array('normal'), array('latin') ),
      'Vesper Libre' => array( array('normal','500','700','900'), array('latin-ext','devanagari','latin') ),
      'Vibur' => array( array('normal'), array('latin') ),
      'Vidaloka' => array( array('normal'), array('latin') ),
      'Viga' => array( array('normal'), array('latin-ext','latin') ),
      'Voces' => array( array('normal'), array('latin-ext','latin') ),
      'Volkhov' => array( array('normal','italic','700','700italic'), array('latin') ),
      'Vollkorn' => array( array('normal','italic','600','600italic','700','700italic','900','900italic'), array('latin-ext','greek','latin','cyrillic','vietnamese','cyrillic-ext') ),
      'Vollkorn SC' => array( array('normal','600','700','900'), array('latin-ext','latin','cyrillic','vietnamese','cyrillic-ext') ),
      'Voltaire' => array( array('normal'), array('latin') ),
      'Waiting for the Sunrise' => array( array('normal'), array('latin') ),
      'Wallpoet' => array( array('normal'), array('latin') ),
      'Walter Turncoat' => array( array('normal'), array('latin') ),
      'Warnes' => array( array('normal'), array('latin-ext','latin') ),
      'Wellfleet' => array( array('normal'), array('latin-ext','latin') ),
      'Wendy One' => array( array('normal'), array('latin-ext','latin') ),
      'Wire One' => array( array('normal'), array('latin') ),
      'Work Sans' => array( array('100','200','300','normal','500','600','700','800','900'), array('latin-ext','latin') ),
      'Yanone Kaffeesatz' => array( array('200','300','normal','700'), array('latin-ext','latin','cyrillic','vietnamese') ),
      'Yantramanav' => array( array('100','300','normal','500','700','900'), array('latin-ext','devanagari','latin') ),
      'Yatra One' => array( array('normal'), array('latin-ext','devanagari','latin') ),
      'Yellowtail' => array( array('normal'), array('latin') ),
      'Yeon Sung' => array( array('normal'), array('latin','korean') ),
      'Yeseva One' => array( array('normal'), array('latin-ext','latin','cyrillic','vietnamese','cyrillic-ext') ),
      'Yesteryear' => array( array('normal'), array('latin') ),
      'Yrsa' => array( array('300','normal','500','600','700'), array('latin-ext','latin') ),
      'Zeyada' => array( array('normal'), array('latin') ),
      'Zilla Slab' => array( array('300','300italic','normal','italic','500','500italic','600','600italic','700','700italic'), array('latin-ext','latin') ),
      'Zilla Slab Highlight' => array( array('normal','700'), array('latin-ext','latin') )
    );
  }
}
